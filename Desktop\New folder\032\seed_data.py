#!/usr/bin/env python3
"""
Sample data seeder for the Qatari Men's Sewing Management System
This script adds sample clients, measurements, and orders for testing
"""

from app import app
from models import db, User, Client, Measurement, Order
from datetime import datetime, date, timedelta
import random

def seed_sample_data():
    """Add sample data to the database"""

    with app.app_context():
        print("🌱 Starting to seed sample data...")

        # Sample clients
        clients_data = [
            {
                'full_name': 'أحمد محمد الكواري',
                'mobile_number': '97455123456',
                'address': 'الدوحة، منطقة الخليج الغربي'
            },
            {
                'full_name': 'محمد عبدالله النعيمي',
                'mobile_number': '97455234567',
                'address': 'الدوحة، منطقة الدفنة'
            },
            {
                'full_name': 'عبدالرحمن سالم المري',
                'mobile_number': '97455345678',
                'address': 'الدوحة، منطقة اللؤلؤة'
            },
            {
                'full_name': 'خالد أحمد الثاني',
                'mobile_number': '97455456789',
                'address': 'الدوحة، منطقة الوكرة'
            },
            {
                'full_name': 'سعد محمد الدوسري',
                'mobile_number': '97455567890',
                'address': 'الدوحة، منطقة الريان'
            }
        ]

        # Add clients
        clients = []
        for client_data in clients_data:
            # Check if client already exists
            existing_client = Client.query.filter_by(mobile_number=client_data['mobile_number']).first()
            if not existing_client:
                client = Client(**client_data)
                db.session.add(client)
                clients.append(client)
                print(f"✅ Added client: {client_data['full_name']}")
            else:
                clients.append(existing_client)
                print(f"⚠️  Client already exists: {client_data['full_name']}")

        db.session.commit()

        # Sample measurements
        fabric_types = ['cotton', 'wool', 'silk', 'linen', 'mixed']

        for client in clients:
            # Check if measurements already exist
            existing_measurement = Measurement.query.filter_by(client_id=client.id).first()
            if not existing_measurement:
                measurement = Measurement(
                    client_id=client.id,
                    neck=random.uniform(38, 45),
                    shoulder=random.uniform(45, 55),
                    sleeve=random.uniform(60, 75),
                    length=random.uniform(140, 160),
                    chest_width=random.uniform(50, 65),
                    zipper_length=random.uniform(15, 25),
                    has_zhengvash=random.choice([True, False]),
                    lower_width=random.uniform(55, 70),
                    fabric_type=random.choice(fabric_types),
                    notes=f"مقاسات خاصة للعميل {client.full_name}"
                )
                db.session.add(measurement)
                print(f"✅ Added measurements for: {client.full_name}")

        db.session.commit()

        # Sample orders
        order_statuses = ['new', 'ongoing', 'ready', 'delivered']

        for i, client in enumerate(clients):
            # Add 1-3 orders per client
            num_orders = random.randint(1, 3)

            for j in range(num_orders):
                # Check if orders already exist for this client
                existing_orders = Order.query.filter_by(client_id=client.id).count()
                if existing_orders < num_orders:
                    order_date = datetime.now() - timedelta(days=random.randint(1, 90))
                    expected_delivery = order_date + timedelta(days=random.randint(7, 21))

                    order = Order(
                        client_id=client.id,
                        order_date=order_date,
                        fabric_type=random.choice(fabric_types),
                        pieces_count=random.randint(1, 5),
                        total_price=random.uniform(200, 800),
                        status=random.choice(order_statuses),
                        expected_delivery=expected_delivery.date(),
                        special_requests=f"طلب خاص رقم {j+1} للعميل {client.full_name}",
                        notes=f"ملاحظات الطلب رقم {j+1}"
                    )

                    # Set actual delivery for delivered orders
                    if order.status == 'delivered':
                        order.actual_delivery = expected_delivery.date() + timedelta(days=random.randint(-2, 5))

                    db.session.add(order)
                    print(f"✅ Added order for: {client.full_name} - Status: {order.status}")

        db.session.commit()

        # Add additional operator user
        operator_user = User.query.filter_by(username='operator').first()
        if not operator_user:
            operator = User(
                username='operator',
                full_name='مشغل النظام',
                role='operator'
            )
            operator.set_password('operator123')
            db.session.add(operator)
            db.session.commit()
            print("✅ Added operator user: operator/operator123")

        print("\n🎉 Sample data seeding completed successfully!")
        print("\n📊 Summary:")
        print(f"   👥 Clients: {Client.query.count()}")
        print(f"   📏 Measurements: {Measurement.query.count()}")
        print(f"   📋 Orders: {Order.query.count()}")
        print(f"   👤 Users: {User.query.count()}")

        print("\n🔑 Login credentials:")
        print("   Admin: admin/admin123")
        print("   Operator: operator/operator123")

        print("\n🌐 Access the system at: http://localhost:5050")

if __name__ == '__main__':
    seed_sample_data()
