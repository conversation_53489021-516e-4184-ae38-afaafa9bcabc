{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h2>
    <div class="text-muted">
        مرحباً، {{ current_user.full_name }}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 text-primary mb-2">
                    <i class="fas fa-users"></i>
                </div>
                <h5 class="card-title">{{ total_clients }}</h5>
                <p class="card-text text-muted">إجمالي العملاء</p>
                <a href="{{ url_for('clients') }}" class="btn btn-outline-primary btn-sm">عرض العملاء</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 text-success mb-2">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h5 class="card-title">{{ total_orders }}</h5>
                <p class="card-text text-muted">إجمالي الطلبات</p>
                <a href="{{ url_for('orders') }}" class="btn btn-outline-success btn-sm">عرض الطلبات</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 text-warning mb-2">
                    <i class="fas fa-clock"></i>
                </div>
                <h5 class="card-title">{{ pending_orders }}</h5>
                <p class="card-text text-muted">طلبات معلقة</p>
                <a href="{{ url_for('orders') }}?status=pending" class="btn btn-outline-warning btn-sm">عرض المعلقة</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 text-info mb-2">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <h5 class="card-title">{{ "%.2f"|format(monthly_revenue) }} ريال</h5>
                <p class="card-text text-muted">إيرادات الشهر</p>
                <a href="{{ url_for('reports') }}" class="btn btn-outline-info btn-sm">عرض التقارير</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Orders -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>الطلبات الأخيرة</h5>
                <a href="{{ url_for('orders') }}" class="btn btn-light btn-sm">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_orders %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العميل</th>
                                <th>نوع القماش</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('view_order', id=order.id) }}" class="text-decoration-none">
                                        #{{ order.id }}
                                    </a>
                                </td>
                                <td>{{ order.client.full_name }}</td>
                                <td>{{ order.fabric_type }}</td>
                                <td>{{ "%.2f"|format(order.total_price) }} ريال</td>
                                <td>
                                    <span class="badge status-{{ order.status }}">
                                        {{ order.get_status_display() }}
                                    </span>
                                </td>
                                <td>{{ order.order_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>لا توجد طلبات حتى الآن</p>
                    <a href="{{ url_for('add_order') }}" class="btn btn-primary">إضافة طلب جديد</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('add_client') }}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                    </a>
                    <a href="{{ url_for('add_order') }}" class="btn btn-outline-success">
                        <i class="fas fa-plus-circle me-2"></i>إضافة طلب جديد
                    </a>
                    <a href="{{ url_for('orders') }}?status=ready" class="btn btn-outline-warning">
                        <i class="fas fa-check-circle me-2"></i>الطلبات الجاهزة
                    </a>
                    <a href="{{ url_for('reports') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-line me-2"></i>تقرير اليوم
                    </a>
                </div>
            </div>
        </div>
        
        <!-- System Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات النظام</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <div class="mb-2">
                        <strong>المستخدم:</strong> {{ current_user.full_name }}
                    </div>
                    <div class="mb-2">
                        <strong>الدور:</strong> {{ current_user.role }}
                    </div>
                    <div class="mb-2">
                        <strong>آخر دخول:</strong> 
                        {% if current_user.last_login %}
                            {{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                            أول مرة
                        {% endif %}
                    </div>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Status Distribution Chart -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع حالات الطلبات</h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>إحصائيات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary">{{ completed_orders }}</h4>
                            <small class="text-muted">طلبات مكتملة</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">{{ pending_orders }}</h4>
                        <small class="text-muted">طلبات معلقة</small>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <h5 class="text-info">{{ "%.1f"|format((completed_orders / total_orders * 100) if total_orders > 0 else 0) }}%</h5>
                    <small class="text-muted">معدل الإنجاز</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Status Distribution Chart
const ctx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['جديد', 'قيد التنفيذ', 'جاهز', 'تم التسليم'],
        datasets: [{
            data: [
                {{ recent_orders|selectattr("status", "equalto", "new")|list|length }},
                {{ recent_orders|selectattr("status", "equalto", "ongoing")|list|length }},
                {{ recent_orders|selectattr("status", "equalto", "ready")|list|length }},
                {{ recent_orders|selectattr("status", "equalto", "delivered")|list|length }}
            ],
            backgroundColor: [
                '#17a2b8',
                '#ffc107',
                '#28a745',
                '#6c757d'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
