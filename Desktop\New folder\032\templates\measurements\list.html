{% extends "base.html" %}

{% block title %}المقاسات - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-ruler me-2"></i>المقاسات</h2>
    <div>
        <a href="{{ url_for('clients') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إضافة مقاسات جديدة
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="{{ search }}" placeholder="البحث بالاسم...">
            </div>
            <div class="col-md-6 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="{{ url_for('measurements') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Measurements Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المقاسات</h5>
    </div>
    <div class="card-body">
        {% if measurements.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>العميل</th>
                        <th>رقم الجوال</th>
                        <th>الرقبة</th>
                        <th>الكتف</th>
                        <th>الكم</th>
                        <th>الطول</th>
                        <th>نوع القماش</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for measurement in measurements.items %}
                    <tr>
                        <td>
                            <a href="{{ url_for('view_client', id=measurement.client.id) }}"
                               class="text-decoration-none">
                                {{ measurement.client.full_name }}
                            </a>
                        </td>
                        <td>{{ measurement.client.mobile_number }}</td>
                        <td>{{ measurement.neck or '-' }} سم</td>
                        <td>{{ measurement.shoulder or '-' }} سم</td>
                        <td>{{ measurement.sleeve or '-' }} سم</td>
                        <td>{{ measurement.length or '-' }} سم</td>
                        <td>{{ measurement.fabric_type or '-' }}</td>
                        <td>{{ measurement.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('view_measurement', id=measurement.id) }}"
                                   class="btn btn-outline-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_measurement', id=measurement.id) }}"
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('add_order') }}?client_id={{ measurement.client.id }}"
                                   class="btn btn-outline-success" title="إضافة طلب">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if measurements.pages > 1 %}
        <nav aria-label="تصفح المقاسات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if measurements.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('measurements', page=measurements.prev_num, search=search) }}">
                        السابق
                    </a>
                </li>
                {% endif %}

                {% for page_num in measurements.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != measurements.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('measurements', page=page_num, search=search) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if measurements.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('measurements', page=measurements.next_num, search=search) }}">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-ruler fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مقاسات</h5>
            <p class="text-muted">
                {% if search %}
                    لم يتم العثور على مقاسات تطابق البحث "{{ search }}"
                {% else %}
                    لم يتم إضافة أي مقاسات بعد
                {% endif %}
            </p>
            <a href="{{ url_for('clients') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مقاسات جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ measurements.total }}</h4>
                        <p class="mb-0">إجمالي المقاسات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-ruler fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ measurements.items|length }}</h4>
                        <p class="mb-0">في هذه الصفحة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ measurements.pages }}</h4>
                        <p class="mb-0">عدد الصفحات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ measurements.page }}</h4>
                        <p class="mb-0">الصفحة الحالية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-bookmark fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
