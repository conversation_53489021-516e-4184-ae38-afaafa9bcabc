{% extends "base.html" %}

{% block title %}{{ client.full_name }} - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user me-2"></i>{{ client.full_name }}</h2>
    <div>
        <a href="{{ url_for('edit_client', id=client.id) }}" class="btn btn-warning">
            <i class="fas fa-edit me-2"></i>تعديل البيانات
        </a>
        <a href="{{ url_for('clients') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للعملاء
        </a>
    </div>
</div>

<div class="row">
    <!-- Client Information -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات العميل</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>الاسم الكامل:</strong><br>
                    <span class="text-muted">{{ client.full_name }}</span>
                </div>
                <div class="mb-3">
                    <strong>رقم الجوال:</strong><br>
                    <span class="text-muted">
                        <i class="fas fa-phone me-1"></i>{{ client.mobile_number }}
                    </span>
                </div>

                {% if client.address %}
                <div class="mb-3">
                    <strong>العنوان:</strong><br>
                    <span class="text-muted">{{ client.address }}</span>
                </div>
                {% endif %}
                <div class="mb-3">
                    <strong>تاريخ التسجيل:</strong><br>
                    <span class="text-muted">{{ client.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
                {% if client.updated_at != client.created_at %}
                <div class="mb-3">
                    <strong>آخر تحديث:</strong><br>
                    <span class="text-muted">{{ client.updated_at.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('add_measurement', client_id=client.id) }}" class="btn btn-outline-success">
                        <i class="fas fa-ruler me-2"></i>إضافة مقاسات
                    </a>
                    <a href="{{ url_for('add_order') }}?client_id={{ client.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus-circle me-2"></i>طلب جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Measurements -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-ruler me-2"></i>المقاسات</h5>
                <div class="btn-group btn-group-sm">
                    {% if measurements %}
                    <a href="{{ url_for('view_measurement', id=measurements[0].id) }}" class="btn btn-primary">
                        <i class="fas fa-eye me-2"></i>عرض المقاسات
                    </a>
                    {% endif %}
                    <a href="{{ url_for('add_measurement', client_id=client.id) }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة مقاسات
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if measurements %}
                    {% for measurement in measurements %}
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="card-title">
                                        مقاسات {{ measurement.created_at.strftime('%Y-%m-%d') }}
                                        {% if measurement.fabric_type %}
                                        <span class="badge bg-info">{{ measurement.fabric_type }}</span>
                                        {% endif %}
                                    </h6>
                                    <div class="row">
                                        {% if measurement.neck %}
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">الرقبة:</small> {{ measurement.neck }} سم
                                        </div>
                                        {% endif %}
                                        {% if measurement.shoulder %}
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">الكتف:</small> {{ measurement.shoulder }} سم
                                        </div>
                                        {% endif %}
                                        {% if measurement.sleeve %}
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">الكم:</small> {{ measurement.sleeve }} سم
                                        </div>
                                        {% endif %}
                                        {% if measurement.length %}
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">الطول:</small> {{ measurement.length }} سم
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row">
                                        {% if measurement.chest_width %}
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">الصدر:</small> {{ measurement.chest_width }} سم
                                        </div>
                                        {% endif %}
                                        {% if measurement.zipper_length %}
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">السحاب:</small> {{ measurement.zipper_length }} سم
                                        </div>
                                        {% endif %}
                                        {% if measurement.lower_width %}
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">الأسفل:</small> {{ measurement.lower_width }} سم
                                        </div>
                                        {% endif %}
                                        {% if measurement.has_zhengvash %}
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">زنجفاش:</small>
                                            <span class="badge bg-success">نعم</span>
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% if measurement.notes %}
                                    <div class="mt-2">
                                        <small class="text-muted">ملاحظات:</small><br>
                                        <small>{{ measurement.notes }}</small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-ruler fa-3x mb-3"></i>
                    <h6>لا توجد مقاسات مسجلة</h6>
                    <p>لم يتم تسجيل أي مقاسات لهذا العميل بعد</p>
                    <a href="{{ url_for('add_measurement', client_id=client.id) }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة مقاسات
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Orders -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>الطلبات</h5>
                <a href="{{ url_for('add_order') }}?client_id={{ client.id }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-2"></i>طلب جديد
                </a>
            </div>
            <div class="card-body">
                {% if orders %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>نوع القماش</th>
                                <th>عدد القطع</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>تاريخ الطلب</th>
                                <th>التسليم المتوقع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in orders %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('view_order', id=order.id) }}" class="text-decoration-none">
                                        #{{ order.id }}
                                    </a>
                                </td>
                                <td>{{ order.fabric_type }}</td>
                                <td>{{ order.pieces_count }}</td>
                                <td>{{ "%.2f"|format(order.total_price) }} ريال</td>
                                <td>
                                    <span class="badge status-{{ order.status }}">
                                        {{ order.get_status_display() }}
                                    </span>
                                </td>
                                <td>{{ order.order_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if order.expected_delivery %}
                                        {{ order.expected_delivery.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('view_order', id=order.id) }}"
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_order', id=order.id) }}"
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                    <h6>لا توجد طلبات</h6>
                    <p>لم يتم تسجيل أي طلبات لهذا العميل بعد</p>
                    <a href="{{ url_for('add_order') }}?client_id={{ client.id }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة طلب جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
