{% extends "base.html" %}

{% block title %}إضافة عميل جديد - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-plus me-2"></i>إضافة عميل جديد</h2>
    <a href="{{ url_for('clients') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للعملاء
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>بيانات العميل</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                            {% if form.full_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.full_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.mobile_number.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                {{ form.mobile_number(class="form-control" + (" is-invalid" if form.mobile_number.errors else "")) }}
                            </div>
                            {% if form.mobile_number.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.mobile_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>



                    <div class="mb-3">
                        {{ form.address.label(class="form-label") }}
                        {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else ""), rows="3") }}
                        {% if form.address.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">العنوان اختياري</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('clients') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ العميل
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Help Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        الاسم الكامل ورقم الجوال مطلوبان
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        العنوان اختياري
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكن إضافة المقاسات بعد حفظ العميل
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        يمكن تعديل البيانات في أي وقت
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-format phone number
document.getElementById('mobile_number').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 0) {
        if (!value.startsWith('974') && !value.startsWith('+974')) {
            if (value.length <= 8) {
                value = '974' + value;
            }
        }
    }
    e.target.value = value;
});

// Focus on first input
document.getElementById('full_name').focus();
</script>
{% endblock %}
