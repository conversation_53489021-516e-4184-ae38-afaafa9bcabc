#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.lib.colors import black, darkblue, lightgrey
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT
import arabic_reshaper
from bidi.algorithm import get_display

class ArabicInvoiceGenerator:
    def __init__(self):
        self.setup_fonts()
        
    def setup_fonts(self):
        """Setup Arabic fonts for PDF generation"""
        try:
            # Try to register Arabic font (you may need to download and place font files)
            # For now, we'll use default fonts with Arabic text reshaping
            pass
        except:
            pass
    
    def reshape_arabic_text(self, text):
        """Reshape Arabic text for proper display in PDF"""
        if not text:
            return ""
        try:
            reshaped_text = arabic_reshaper.reshape(str(text))
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return str(text)
    
    def generate_invoice(self, order, output_path=None):
        """Generate Arabic invoice PDF for an order"""
        if not output_path:
            output_path = f"invoice_{order.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        # Create PDF document with A4 size and optimized Arabic RTL margins
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=2*cm,    # Right margin for Arabic text start
            leftMargin=2*cm,     # Left margin for balance
            topMargin=2.5*cm,    # Top margin for header
            bottomMargin=2.5*cm, # Bottom margin for footer
            title=f"فاتورة رقم {order.id}",
            author="نظام إدارة خياطة الرجال القطري",
            subject=f"فاتورة طلب رقم {order.id}"
        )
        
        # Build content
        story = []
        styles = getSampleStyleSheet()
        
        # Create custom Arabic styles with proper RTL alignment
        arabic_title = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            alignment=TA_CENTER,
            fontSize=20,
            spaceAfter=25,
            textColor=darkblue,
            fontName='Helvetica-Bold'
        )

        arabic_heading = ParagraphStyle(
            'ArabicHeading',
            parent=styles['Heading2'],
            alignment=TA_RIGHT,
            fontSize=16,
            spaceAfter=12,
            textColor=darkblue,
            fontName='Helvetica-Bold',
            rightIndent=0,
            leftIndent=0
        )

        arabic_normal = ParagraphStyle(
            'ArabicNormal',
            parent=styles['Normal'],
            alignment=TA_RIGHT,
            fontSize=12,
            spaceAfter=8,
            rightIndent=0,
            leftIndent=0,
            fontName='Helvetica'
        )

        arabic_center = ParagraphStyle(
            'ArabicCenter',
            parent=styles['Normal'],
            alignment=TA_CENTER,
            fontSize=12,
            spaceAfter=8,
            fontName='Helvetica'
        )
        
        # Company Header with enhanced Arabic formatting
        company_name = self.reshape_arabic_text("نظام إدارة خياطة الرجال القطري")
        story.append(Paragraph(company_name, arabic_title))

        company_info = self.reshape_arabic_text("الدوحة، دولة قطر | هاتف: +974 XXXX XXXX | البريد الإلكتروني: <EMAIL>")
        story.append(Paragraph(company_info, arabic_center))
        story.append(Spacer(1, 25))

        # Invoice Title with decorative styling
        invoice_title = self.reshape_arabic_text("فــــاتــــورة")
        story.append(Paragraph(invoice_title, arabic_title))
        story.append(Spacer(1, 25))
        
        # Invoice Info Table with better Arabic layout
        invoice_data = [
            [self.reshape_arabic_text("رقم الفاتورة:"), f"#{order.id}"],
            [self.reshape_arabic_text("تاريخ الفاتورة:"), datetime.now().strftime('%Y-%m-%d')],
            [self.reshape_arabic_text("تاريخ الطلب:"), order.order_date.strftime('%Y-%m-%d')],
            [self.reshape_arabic_text("حالة الطلب:"), self.reshape_arabic_text(order.get_status_display())]
        ]

        invoice_table = Table(invoice_data, colWidths=[5*cm, 5*cm])
        invoice_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),  # Labels right-aligned
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),   # Values left-aligned
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 0.5, lightgrey),
            ('BACKGROUND', (0, 0), (0, -1), lightgrey),  # Label column background
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),  # Bold labels
        ]))
        story.append(invoice_table)
        story.append(Spacer(1, 20))
        
        # Client Information
        client_title = self.reshape_arabic_text("معلومات العميل")
        story.append(Paragraph(client_title, arabic_heading))
        
        client_data = [
            [self.reshape_arabic_text("اسم العميل:"), self.reshape_arabic_text(order.client.full_name)],
            [self.reshape_arabic_text("رقم الجوال:"), order.client.mobile_number],
        ]

        if order.client.address:
            client_data.append([self.reshape_arabic_text("العنوان:"), self.reshape_arabic_text(order.client.address)])

        client_table = Table(client_data, colWidths=[5*cm, 9*cm])
        client_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),  # Labels right-aligned
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),  # Values right-aligned for Arabic
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, black),
            ('BACKGROUND', (0, 0), (0, -1), lightgrey),  # Label column background
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),  # Bold labels
        ]))
        story.append(client_table)
        story.append(Spacer(1, 20))
        
        # Order Details
        order_title = self.reshape_arabic_text("تفاصيل الطلب")
        story.append(Paragraph(order_title, arabic_heading))
        
        # Order items table with proper Arabic RTL layout
        order_headers = [
            self.reshape_arabic_text("الوصف"),
            self.reshape_arabic_text("الكمية"),
            self.reshape_arabic_text("سعر الوحدة"),
            self.reshape_arabic_text("المجموع")
        ]

        order_data = [order_headers]

        # Add order item with proper Arabic formatting
        unit_price = order.total_price / order.pieces_count if order.pieces_count > 0 else order.total_price
        order_data.append([
            self.reshape_arabic_text(order.fabric_type),
            str(order.pieces_count),
            f"{unit_price:.2f} ريال",
            f"{order.total_price:.2f} ريال"
        ])

        # Optimized column widths for A4 Arabic layout
        order_table = Table(order_data, colWidths=[7*cm, 2.5*cm, 3*cm, 3.5*cm])
        order_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),   # Description right-aligned (Arabic)
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),  # Quantity centered
            ('ALIGN', (2, 0), (2, -1), 'CENTER'),  # Unit price centered
            ('ALIGN', (3, 0), (3, -1), 'CENTER'),  # Total centered
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
            ('TOPPADDING', (0, 0), (-1, -1), 15),
            ('GRID', (0, 0), (-1, -1), 1, black),
            ('BACKGROUND', (0, 0), (-1, 0), lightgrey),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),  # Bold headers
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # Vertical center alignment
        ]))
        story.append(order_table)
        story.append(Spacer(1, 20))
        
        # Total Section with optimized Arabic RTL formatting
        total_data = [
            [self.reshape_arabic_text("المجموع الفرعي:"), f"{order.total_price:.2f} ريال"],
            [self.reshape_arabic_text("الضريبة (15%):"), f"{order.total_price * 0.15:.2f} ريال"],
            [self.reshape_arabic_text("المجموع الكلي:"), f"{order.total_price * 1.15:.2f} ريال"]
        ]

        # Positioned to the right side for Arabic layout
        total_table = Table(total_data, colWidths=[8*cm, 4*cm])
        total_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),   # Labels right-aligned (Arabic)
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),   # Values right-aligned for consistency
            ('FONTSIZE', (0, 0), (-1, -1), 13),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
            ('TOPPADDING', (0, 0), (-1, -1), 15),
            ('GRID', (0, 0), (-1, -1), 1.5, black),
            ('BACKGROUND', (0, 0), (0, -1), lightgrey),  # Label column background
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),  # Bold labels
            ('LINEBELOW', (0, -1), (-1, -1), 3, darkblue),  # Thick line under total
            ('FONTSIZE', (0, -1), (-1, -1), 16),  # Larger font for total
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),  # Bold total
            ('BACKGROUND', (0, -1), (-1, -1), lightgrey),  # Total row background
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # Vertical center alignment
        ]))
        story.append(total_table)
        story.append(Spacer(1, 30))
        
        # Special requests and notes
        if order.special_requests:
            special_title = self.reshape_arabic_text("طلبات خاصة")
            story.append(Paragraph(special_title, arabic_heading))
            special_text = self.reshape_arabic_text(order.special_requests)
            story.append(Paragraph(special_text, arabic_normal))
            story.append(Spacer(1, 15))
        
        if order.notes:
            notes_title = self.reshape_arabic_text("ملاحظات")
            story.append(Paragraph(notes_title, arabic_heading))
            notes_text = self.reshape_arabic_text(order.notes)
            story.append(Paragraph(notes_text, arabic_normal))
            story.append(Spacer(1, 15))
        
        # Footer with enhanced styling
        story.append(Spacer(1, 20))
        footer_text = self.reshape_arabic_text("شكراً لتعاملكم معنا")

        # Create footer style
        footer_style = ParagraphStyle(
            'FooterStyle',
            parent=arabic_center,
            fontSize=14,
            textColor=darkblue,
            fontName='Helvetica-Bold',
            spaceAfter=10
        )

        story.append(Paragraph(footer_text, footer_style))

        # Add contact info
        contact_text = self.reshape_arabic_text("للاستفسارات: +974 XXXX XXXX | البريد الإلكتروني: <EMAIL>")
        contact_style = ParagraphStyle(
            'ContactStyle',
            parent=arabic_center,
            fontSize=10,
            textColor=black
        )
        story.append(Paragraph(contact_text, contact_style))
        
        # Build PDF
        doc.build(story)
        return output_path
