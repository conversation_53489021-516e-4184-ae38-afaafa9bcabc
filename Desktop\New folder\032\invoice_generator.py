#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.lib.colors import black, darkblue, lightgrey
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT
import arabic_reshaper
from bidi.algorithm import get_display

class ArabicInvoiceGenerator:
    def __init__(self):
        self.setup_fonts()
        
    def setup_fonts(self):
        """Setup Arabic fonts for PDF generation"""
        try:
            # Try to register Arabic font (you may need to download and place font files)
            # For now, we'll use default fonts with Arabic text reshaping
            pass
        except:
            pass
    
    def reshape_arabic_text(self, text):
        """Reshape Arabic text for proper display in PDF"""
        if not text:
            return ""
        try:
            reshaped_text = arabic_reshaper.reshape(str(text))
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return str(text)
    
    def generate_invoice(self, order, output_path=None):
        """Generate Arabic invoice PDF for an order"""
        if not output_path:
            output_path = f"invoice_{order.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        # Create PDF document
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )
        
        # Build content
        story = []
        styles = getSampleStyleSheet()
        
        # Create custom Arabic styles
        arabic_title = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            alignment=TA_CENTER,
            fontSize=18,
            spaceAfter=20,
            textColor=darkblue
        )
        
        arabic_heading = ParagraphStyle(
            'ArabicHeading',
            parent=styles['Heading2'],
            alignment=TA_RIGHT,
            fontSize=14,
            spaceAfter=10,
            textColor=darkblue
        )
        
        arabic_normal = ParagraphStyle(
            'ArabicNormal',
            parent=styles['Normal'],
            alignment=TA_RIGHT,
            fontSize=11,
            spaceAfter=6
        )
        
        arabic_center = ParagraphStyle(
            'ArabicCenter',
            parent=styles['Normal'],
            alignment=TA_CENTER,
            fontSize=11,
            spaceAfter=6
        )
        
        # Company Header
        company_name = self.reshape_arabic_text("نظام إدارة خياطة الرجال القطري")
        story.append(Paragraph(company_name, arabic_title))
        
        company_info = self.reshape_arabic_text("الدوحة، قطر | هاتف: +974 XXXX XXXX")
        story.append(Paragraph(company_info, arabic_center))
        story.append(Spacer(1, 20))
        
        # Invoice Title
        invoice_title = self.reshape_arabic_text("فاتورة")
        story.append(Paragraph(invoice_title, arabic_title))
        story.append(Spacer(1, 20))
        
        # Invoice Info Table
        invoice_data = [
            [self.reshape_arabic_text("رقم الفاتورة:"), f"#{order.id}"],
            [self.reshape_arabic_text("تاريخ الفاتورة:"), datetime.now().strftime('%Y-%m-%d')],
            [self.reshape_arabic_text("تاريخ الطلب:"), order.order_date.strftime('%Y-%m-%d')],
            [self.reshape_arabic_text("حالة الطلب:"), self.reshape_arabic_text(order.get_status_display())]
        ]
        
        invoice_table = Table(invoice_data, colWidths=[4*cm, 6*cm])
        invoice_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
        ]))
        story.append(invoice_table)
        story.append(Spacer(1, 20))
        
        # Client Information
        client_title = self.reshape_arabic_text("معلومات العميل")
        story.append(Paragraph(client_title, arabic_heading))
        
        client_data = [
            [self.reshape_arabic_text("اسم العميل:"), self.reshape_arabic_text(order.client.full_name)],
            [self.reshape_arabic_text("رقم الجوال:"), order.client.mobile_number],
        ]
        
        if order.client.address:
            client_data.append([self.reshape_arabic_text("العنوان:"), self.reshape_arabic_text(order.client.address)])
        
        client_table = Table(client_data, colWidths=[4*cm, 10*cm])
        client_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 0.5, lightgrey),
        ]))
        story.append(client_table)
        story.append(Spacer(1, 20))
        
        # Order Details
        order_title = self.reshape_arabic_text("تفاصيل الطلب")
        story.append(Paragraph(order_title, arabic_heading))
        
        # Order items table
        order_headers = [
            self.reshape_arabic_text("المجموع"),
            self.reshape_arabic_text("السعر الوحدة"),
            self.reshape_arabic_text("الكمية"),
            self.reshape_arabic_text("الوصف")
        ]
        
        order_data = [order_headers]
        
        # Add order item
        unit_price = order.total_price / order.pieces_count if order.pieces_count > 0 else order.total_price
        order_data.append([
            f"{order.total_price:.2f} ريال",
            f"{unit_price:.2f} ريال", 
            str(order.pieces_count),
            self.reshape_arabic_text(order.fabric_type)
        ])
        
        order_table = Table(order_data, colWidths=[3*cm, 3*cm, 2*cm, 6*cm])
        order_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('TOPPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, black),
            ('BACKGROUND', (0, 0), (-1, 0), lightgrey),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('ALIGN', (3, 0), (3, -1), 'RIGHT'),  # Description column right-aligned
        ]))
        story.append(order_table)
        story.append(Spacer(1, 20))
        
        # Total Section
        total_data = [
            [self.reshape_arabic_text("المجموع الفرعي:"), f"{order.total_price:.2f} ريال"],
            [self.reshape_arabic_text("الضريبة (15%):"), f"{order.total_price * 0.15:.2f} ريال"],
            [self.reshape_arabic_text("المجموع الكلي:"), f"{order.total_price * 1.15:.2f} ريال"]
        ]
        
        total_table = Table(total_data, colWidths=[6*cm, 4*cm])
        total_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
            ('LINEBELOW', (0, -1), (-1, -1), 2, black),
            ('FONTSIZE', (0, -1), (-1, -1), 14),
        ]))
        story.append(total_table)
        story.append(Spacer(1, 30))
        
        # Special requests and notes
        if order.special_requests:
            special_title = self.reshape_arabic_text("طلبات خاصة")
            story.append(Paragraph(special_title, arabic_heading))
            special_text = self.reshape_arabic_text(order.special_requests)
            story.append(Paragraph(special_text, arabic_normal))
            story.append(Spacer(1, 15))
        
        if order.notes:
            notes_title = self.reshape_arabic_text("ملاحظات")
            story.append(Paragraph(notes_title, arabic_heading))
            notes_text = self.reshape_arabic_text(order.notes)
            story.append(Paragraph(notes_text, arabic_normal))
            story.append(Spacer(1, 15))
        
        # Footer
        footer_text = self.reshape_arabic_text("شكراً لتعاملكم معنا")
        story.append(Paragraph(footer_text, arabic_center))
        
        # Build PDF
        doc.build(story)
        return output_path
