{% extends "base.html" %}

{% block title %}طلب #{{ order.id }} - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-shopping-cart me-2"></i>طلب #{{ order.id }}</h2>
    <div>
        <a href="{{ url_for('edit_order', id=order.id) }}" class="btn btn-warning">
            <i class="fas fa-edit me-2"></i>تعديل الطلب
        </a>
        {% if order.status in ['ready', 'delivered'] %}
        <div class="btn-group">
            <a href="{{ url_for('view_invoice_html', id=order.id) }}" class="btn btn-info" target="_blank">
                <i class="fas fa-eye me-2"></i>عرض الفاتورة
            </a>
            <a href="{{ url_for('generate_invoice', id=order.id) }}" class="btn btn-success" target="_blank">
                <i class="fas fa-download me-2"></i>تحميل PDF
            </a>
        </div>
        {% endif %}
        <a href="{{ url_for('orders') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للطلبات
        </a>
    </div>
</div>

<div class="row">
    <!-- Order Information -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>تفاصيل الطلب</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <strong>رقم الطلب:</strong><br>
                            <span class="text-muted">#{{ order.id }}</span>
                        </div>
                        <div class="mb-3">
                            <strong>العميل:</strong><br>
                            <a href="{{ url_for('view_client', id=order.client.id) }}" class="text-decoration-none">
                                {{ order.client.full_name }}
                            </a>
                        </div>
                        <div class="mb-3">
                            <strong>نوع القماش:</strong><br>
                            <span class="text-muted">{{ order.fabric_type }}</span>
                        </div>
                        <div class="mb-3">
                            <strong>عدد القطع:</strong><br>
                            <span class="text-muted">{{ order.pieces_count }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <strong>السعر الإجمالي:</strong><br>
                            <span class="text-success h5">{{ "%.2f"|format(order.total_price) }} ريال</span>
                        </div>
                        <div class="mb-3">
                            <strong>تاريخ الطلب:</strong><br>
                            <span class="text-muted">{{ order.order_date.strftime('%Y-%m-%d %H:%M') }}</span>
                        </div>
                        <div class="mb-3">
                            <strong>التسليم المتوقع:</strong><br>
                            {% if order.expected_delivery %}
                                <span class="text-muted">{{ order.expected_delivery.strftime('%Y-%m-%d') }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            <strong>الحالة:</strong><br>
                            <span class="badge bg-{{ order.get_status_color() }} fs-6">
                                {{ order.get_status_display() }}
                            </span>
                        </div>
                    </div>
                </div>

                {% if order.special_requests %}
                <hr>
                <div class="mb-3">
                    <strong>طلبات خاصة:</strong><br>
                    <div class="bg-light p-3 rounded">
                        {{ order.special_requests }}
                    </div>
                </div>
                {% endif %}

                {% if order.notes %}
                <div class="mb-3">
                    <strong>ملاحظات:</strong><br>
                    <div class="bg-light p-3 rounded">
                        {{ order.notes|replace('\n', '<br>')|safe }}
                    </div>
                </div>
                {% endif %}

                {% if order.fabric_photo_path %}
                <hr>
                <div class="mb-3">
                    <strong>صورة القماش:</strong><br>
                    <img src="{{ url_for('static', filename='uploads/' + order.fabric_photo_path) }}"
                         class="img-thumbnail mt-2" style="max-width: 300px;">
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Status Management -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-tasks me-2"></i>إدارة الحالة</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>الحالة الحالية:</strong><br>
                    <span class="badge bg-{{ order.get_status_color() }} fs-6">
                        {{ order.get_status_display() }}
                    </span>
                </div>

                {% if order.actual_delivery %}
                <div class="mb-3">
                    <strong>تاريخ التسليم الفعلي:</strong><br>
                    <span class="text-success">{{ order.actual_delivery.strftime('%Y-%m-%d') }}</span>
                </div>
                {% endif %}

                <div class="d-grid gap-2">
                    <a href="{{ url_for('update_order_status', id=order.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>تحديث الحالة
                    </a>
                    {% if current_user.is_admin() %}
                    <button type="button" class="btn btn-danger" onclick="confirmDelete({{ order.id }})">
                        <i class="fas fa-trash me-2"></i>حذف الطلب
                    </button>
                    {% endif %}
                </div>

                <!-- Quick Status Actions -->
                {% if order.status == 'new' %}
                <div class="mt-3">
                    <small class="text-muted">إجراءات سريعة:</small>
                    <form method="POST" action="{{ url_for('update_order_status', id=order.id) }}" class="mt-2">
                        <input type="hidden" name="status" value="ongoing">
                        <input type="hidden" name="notes" value="تم بدء العمل على الطلب">
                        <button type="submit" class="btn btn-outline-warning btn-sm w-100">
                            <i class="fas fa-play me-1"></i>بدء العمل
                        </button>
                    </form>
                </div>
                {% elif order.status == 'ongoing' %}
                <div class="mt-3">
                    <small class="text-muted">إجراءات سريعة:</small>
                    <form method="POST" action="{{ url_for('update_order_status', id=order.id) }}" class="mt-2">
                        <input type="hidden" name="status" value="ready">
                        <input type="hidden" name="notes" value="الطلب جاهز للتسليم">
                        <button type="submit" class="btn btn-outline-success btn-sm w-100">
                            <i class="fas fa-check me-1"></i>جاهز للتسليم
                        </button>
                    </form>
                </div>
                {% elif order.status == 'ready' %}
                <div class="mt-3">
                    <small class="text-muted">إجراءات سريعة:</small>
                    <form method="POST" action="{{ url_for('update_order_status', id=order.id) }}" class="mt-2">
                        <input type="hidden" name="status" value="delivered">
                        <input type="hidden" name="notes" value="تم تسليم الطلب للعميل">
                        <button type="submit" class="btn btn-outline-secondary btn-sm w-100"
                                onclick="return confirm('هل تم تسليم الطلب فعلاً للعميل؟')">
                            <i class="fas fa-handshake me-1"></i>تم التسليم
                        </button>
                    </form>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Client Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل</h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>الاسم:</strong><br>
                    <small>{{ order.client.full_name }}</small>
                </div>
                <div class="mb-2">
                    <strong>الجوال:</strong><br>
                    <small>{{ order.client.mobile_number }}</small>
                </div>

                <div class="d-grid mt-3">
                    <a href="{{ url_for('view_client', id=order.client.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-2"></i>عرض العميل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(orderId) {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/orders/${orderId}/delete`;

        // Add CSRF token
        const csrfToken = document.querySelector('[name=csrf_token]');
        if (csrfToken) {
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = 'csrf_token';
            tokenInput.value = csrfToken.value;
            form.appendChild(tokenInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
