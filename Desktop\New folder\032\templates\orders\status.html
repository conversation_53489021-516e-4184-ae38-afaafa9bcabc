{% extends "base.html" %}

{% block title %}تحديث حالة الطلب #{{ order.id }} - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tasks me-2"></i>تحديث حالة الطلب #{{ order.id }}</h2>
    <div>
        <a href="{{ url_for('view_order', id=order.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للطلب
        </a>
    </div>
</div>

<div class="row">
    <!-- Status Update Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>تحديث الحالة
                    <span class="badge bg-{{ order.get_status_color() }} ms-2">{{ order.get_status_display() }}</span>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <!-- Current Status Info -->
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>الحالة الحالية:</strong>
                                <span class="badge bg-{{ order.get_status_color() }} ms-2">{{ order.get_status_display() }}</span>
                            </div>
                            <div class="col-md-6">
                                <strong>آخر تحديث:</strong>
                                <small class="text-muted">{{ order.updated_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else ""), id="status-select") }}
                            {% if form.status.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.status.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text" id="status-help">
                                اختر الحالة الجديدة للطلب
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3" id="delivery-date-field" style="display: none;">
                            {{ form.actual_delivery.label(class="form-label") }}
                            {{ form.actual_delivery(class="form-control" + (" is-invalid" if form.actual_delivery.errors else "")) }}
                            {% if form.actual_delivery.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.actual_delivery.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                سيتم تعيين تاريخ اليوم تلقائياً إذا تُرك فارغاً
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="4", placeholder="أضف ملاحظات حول تحديث الحالة...") }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.notify_client(class="form-check-input") }}
                            {{ form.notify_client.label(class="form-check-label") }}
                        </div>
                        <div class="form-text">
                            سيتم إرسال رسالة نصية للعميل عند التحديث (قريباً)
                        </div>
                    </div>
                    
                    <!-- Status Flow Guide -->
                    <div class="alert alert-light border">
                        <h6><i class="fas fa-route me-2"></i>مسار حالات الطلب:</h6>
                        <div class="d-flex align-items-center flex-wrap">
                            <span class="badge bg-primary me-2 mb-1">جديد</span>
                            <i class="fas fa-arrow-left text-muted me-2"></i>
                            <span class="badge bg-warning me-2 mb-1">قيد التنفيذ</span>
                            <i class="fas fa-arrow-left text-muted me-2"></i>
                            <span class="badge bg-success me-2 mb-1">جاهز</span>
                            <i class="fas fa-arrow-left text-muted me-2"></i>
                            <span class="badge bg-secondary me-2 mb-1">تم التسليم</span>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            يمكن التنقل بين الحالات حسب المسار المحدد. لا يمكن تغيير الحالة من "تم التسليم".
                        </small>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('view_order', id=order.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary" id="update-btn">
                            <i class="fas fa-save me-2"></i>تحديث الحالة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Order Info & History -->
    <div class="col-md-4">
        <!-- Order Summary -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>ملخص الطلب</h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>العميل:</strong><br>
                    <a href="{{ url_for('view_client', id=order.client.id) }}" class="text-decoration-none">
                        {{ order.client.full_name }}
                    </a>
                </div>
                <div class="mb-2">
                    <strong>نوع القماش:</strong><br>
                    <span class="text-muted">{{ order.fabric_type }}</span>
                </div>
                <div class="mb-2">
                    <strong>السعر:</strong><br>
                    <span class="text-success">{{ "%.2f"|format(order.total_price) }} ريال</span>
                </div>
                <div class="mb-2">
                    <strong>تاريخ الطلب:</strong><br>
                    <span class="text-muted">{{ order.order_date.strftime('%Y-%m-%d') }}</span>
                </div>
                {% if order.expected_delivery %}
                <div class="mb-2">
                    <strong>التسليم المتوقع:</strong><br>
                    <span class="text-muted">{{ order.expected_delivery.strftime('%Y-%m-%d') }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Status History -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-history me-2"></i>تاريخ التغييرات</h6>
            </div>
            <div class="card-body">
                {% if order.status_history %}
                <div class="timeline">
                    {% for change in order.status_history|reverse %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-{{ 'success' if change.new_status == 'delivered' else 'primary' }}"></div>
                        <div class="timeline-content">
                            <div class="d-flex justify-content-between">
                                <strong>{{ change.new_status|replace('new', 'جديد')|replace('ongoing', 'قيد التنفيذ')|replace('ready', 'جاهز')|replace('delivered', 'تم التسليم') }}</strong>
                                <small class="text-muted">{{ change.changed_at.strftime('%m-%d %H:%M') }}</small>
                            </div>
                            {% if change.old_status %}
                            <small class="text-muted">
                                من: {{ change.old_status|replace('new', 'جديد')|replace('ongoing', 'قيد التنفيذ')|replace('ready', 'جاهز')|replace('delivered', 'تم التسليم') }}
                            </small>
                            {% endif %}
                            {% if change.change_reason %}
                            <div class="mt-1">
                                <small>{{ change.change_reason }}</small>
                            </div>
                            {% endif %}
                            <div class="mt-1">
                                <small class="text-muted">بواسطة: {{ change.user.full_name }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-history fa-2x mb-2"></i>
                    <p class="mb-0">لا يوجد تاريخ تغييرات</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -37px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status-select');
    const deliveryField = document.getElementById('delivery-date-field');
    const updateBtn = document.getElementById('update-btn');
    const currentStatus = '{{ order.status }}';
    
    // Show/hide delivery date field
    function toggleDeliveryField() {
        if (statusSelect.value === 'delivered') {
            deliveryField.style.display = 'block';
        } else {
            deliveryField.style.display = 'none';
        }
    }
    
    // Update button text based on status change
    function updateButtonText() {
        if (statusSelect.value !== currentStatus) {
            updateBtn.innerHTML = '<i class="fas fa-exchange-alt me-2"></i>تغيير الحالة';
            updateBtn.className = 'btn btn-warning';
        } else {
            updateBtn.innerHTML = '<i class="fas fa-save me-2"></i>تحديث الحالة';
            updateBtn.className = 'btn btn-primary';
        }
    }
    
    // Validate status change
    function validateStatusChange() {
        const allowedChanges = {
            'new': ['ongoing', 'ready'],
            'ongoing': ['ready', 'new'],
            'ready': ['delivered', 'ongoing'],
            'delivered': []
        };
        
        const newStatus = statusSelect.value;
        const helpText = document.getElementById('status-help');
        
        if (newStatus === currentStatus) {
            helpText.textContent = 'الحالة الحالية';
            helpText.className = 'form-text text-muted';
            return true;
        }
        
        if (allowedChanges[currentStatus].includes(newStatus)) {
            helpText.textContent = 'تغيير مسموح';
            helpText.className = 'form-text text-success';
            return true;
        } else {
            helpText.textContent = 'تغيير غير مسموح من الحالة الحالية';
            helpText.className = 'form-text text-danger';
            return false;
        }
    }
    
    statusSelect.addEventListener('change', function() {
        toggleDeliveryField();
        updateButtonText();
        validateStatusChange();
    });
    
    // Initialize
    toggleDeliveryField();
    updateButtonText();
    validateStatusChange();
    
    // Set today's date as default for delivery
    if (statusSelect.value === 'delivered') {
        const deliveryInput = document.getElementById('actual_delivery');
        if (!deliveryInput.value) {
            deliveryInput.value = new Date().toISOString().split('T')[0];
        }
    }
});
</script>
{% endblock %}
