#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db
from models import PricingRule, SystemSettings

def seed_pricing_data():
    """Add sample pricing rules and system settings"""
    
    with app.app_context():
        print("🏷️  Adding pricing rules...")
        
        # Sample pricing rules
        pricing_rules = [
            {
                'fabric_type': 'ثوب صيفي قطني',
                'base_price': 250.00,
                'size_multiplier': 1.0,
                'rush_multiplier': 1.5,
                'is_active': True
            },
            {
                'fabric_type': 'ثوب شتوي صوفي',
                'base_price': 350.00,
                'size_multiplier': 1.1,
                'rush_multiplier': 1.6,
                'is_active': True
            },
            {
                'fabric_type': 'بشت عادي',
                'base_price': 450.00,
                'size_multiplier': 1.2,
                'rush_multiplier': 1.7,
                'is_active': True
            },
            {
                'fabric_type': 'بشت فاخر مطرز',
                'base_price': 800.00,
                'size_multiplier': 1.3,
                'rush_multiplier': 1.8,
                'is_active': True
            },
            {
                'fabric_type': 'دشداشة كويتية',
                'base_price': 300.00,
                'size_multiplier': 1.0,
                'rush_multiplier': 1.5,
                'is_active': True
            },
            {
                'fabric_type': 'جلابية مصرية',
                'base_price': 200.00,
                'size_multiplier': 0.9,
                'rush_multiplier': 1.4,
                'is_active': True
            },
            {
                'fabric_type': 'قميص تراثي',
                'base_price': 180.00,
                'size_multiplier': 0.8,
                'rush_multiplier': 1.3,
                'is_active': True
            },
            {
                'fabric_type': 'عباءة رجالية',
                'base_price': 400.00,
                'size_multiplier': 1.1,
                'rush_multiplier': 1.6,
                'is_active': True
            }
        ]
        
        for rule_data in pricing_rules:
            existing_rule = PricingRule.query.filter_by(fabric_type=rule_data['fabric_type']).first()
            if not existing_rule:
                rule = PricingRule(**rule_data)
                db.session.add(rule)
                print(f"✅ Added pricing rule: {rule_data['fabric_type']} - {rule_data['base_price']} ريال")
            else:
                print(f"⚠️  Pricing rule already exists: {rule_data['fabric_type']}")
        
        print("\n⚙️  Adding system settings...")
        
        # Sample system settings
        system_settings = [
            {
                'setting_key': 'company_name',
                'setting_value': 'نظام إدارة خياطة الرجال القطري',
                'setting_type': 'text',
                'description': 'اسم الشركة',
                'category': 'company'
            },
            {
                'setting_key': 'company_address',
                'setting_value': 'الدوحة، قطر',
                'setting_type': 'text',
                'description': 'عنوان الشركة',
                'category': 'company'
            },
            {
                'setting_key': 'company_phone',
                'setting_value': '+974 XXXX XXXX',
                'setting_type': 'text',
                'description': 'هاتف الشركة',
                'category': 'company'
            },
            {
                'setting_key': 'company_email',
                'setting_value': '<EMAIL>',
                'setting_type': 'text',
                'description': 'بريد الشركة',
                'category': 'company'
            },
            {
                'setting_key': 'default_delivery_days',
                'setting_value': '7',
                'setting_type': 'number',
                'description': 'أيام التسليم الافتراضية',
                'category': 'pricing'
            },
            {
                'setting_key': 'rush_order_days',
                'setting_value': '3',
                'setting_type': 'number',
                'description': 'أيام الطلب العاجل',
                'category': 'pricing'
            },
            {
                'setting_key': 'minimum_order_amount',
                'setting_value': '50.0',
                'setting_type': 'number',
                'description': 'الحد الأدنى للطلب',
                'category': 'pricing'
            },
            {
                'setting_key': 'bulk_discount_threshold',
                'setting_value': '5',
                'setting_type': 'number',
                'description': 'عدد القطع للخصم الجماعي',
                'category': 'discount'
            },
            {
                'setting_key': 'bulk_discount_percentage',
                'setting_value': '10.0',
                'setting_type': 'number',
                'description': 'نسبة الخصم الجماعي',
                'category': 'discount'
            },
            {
                'setting_key': 'vip_discount_percentage',
                'setting_value': '15.0',
                'setting_type': 'number',
                'description': 'خصم العملاء المميزين',
                'category': 'discount'
            },
            {
                'setting_key': 'tax_enabled',
                'setting_value': 'true',
                'setting_type': 'boolean',
                'description': 'تفعيل الضريبة',
                'category': 'tax'
            },
            {
                'setting_key': 'tax_percentage',
                'setting_value': '15.0',
                'setting_type': 'number',
                'description': 'نسبة الضريبة',
                'category': 'tax'
            },
            {
                'setting_key': 'tax_number',
                'setting_value': '*********',
                'setting_type': 'text',
                'description': 'الرقم الضريبي',
                'category': 'tax'
            },
            {
                'setting_key': 'sms_notifications',
                'setting_value': 'true',
                'setting_type': 'boolean',
                'description': 'إشعارات SMS',
                'category': 'notifications'
            },
            {
                'setting_key': 'email_notifications',
                'setting_value': 'false',
                'setting_type': 'boolean',
                'description': 'إشعارات البريد الإلكتروني',
                'category': 'notifications'
            },
            {
                'setting_key': 'whatsapp_notifications',
                'setting_value': 'false',
                'setting_type': 'boolean',
                'description': 'إشعارات واتساب',
                'category': 'notifications'
            },
            {
                'setting_key': 'auto_backup',
                'setting_value': 'true',
                'setting_type': 'boolean',
                'description': 'النسخ الاحتياطي التلقائي',
                'category': 'system'
            },
            {
                'setting_key': 'backup_frequency',
                'setting_value': 'weekly',
                'setting_type': 'text',
                'description': 'تكرار النسخ الاحتياطي',
                'category': 'system'
            }
        ]
        
        for setting_data in system_settings:
            existing_setting = SystemSettings.query.filter_by(setting_key=setting_data['setting_key']).first()
            if not existing_setting:
                setting = SystemSettings(**setting_data)
                db.session.add(setting)
                print(f"✅ Added system setting: {setting_data['setting_key']}")
            else:
                print(f"⚠️  System setting already exists: {setting_data['setting_key']}")
        
        db.session.commit()
        
        print("\n🎉 Pricing and settings data seeding completed successfully!")
        
        # Summary
        pricing_count = PricingRule.query.count()
        settings_count = SystemSettings.query.count()
        
        print(f"\n📊 Summary:")
        print(f"   🏷️  Pricing Rules: {pricing_count}")
        print(f"   ⚙️  System Settings: {settings_count}")

if __name__ == '__main__':
    seed_pricing_data()
