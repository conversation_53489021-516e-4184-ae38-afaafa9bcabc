{% extends "base.html" %}

{% block title %}إعدادات النظام - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-cog me-2"></i>إعدادات النظام</h2>
</div>

<!-- Settings Categories -->
<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-tags fa-3x text-primary mb-3"></i>
                <h5>إعدادات الأسعار</h5>
                <p class="text-muted">إدارة قواعد التسعير وحاسبة الأسعار</p>
                <a href="{{ url_for('pricing_settings') }}" class="btn btn-primary">
                    <i class="fas fa-cog me-2"></i>إدارة الأسعار
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-sliders-h fa-3x text-success mb-3"></i>
                <h5>إعدادات النظام</h5>
                <p class="text-muted">إعدادات الشركة والنظام والإشعارات</p>
                <a href="{{ url_for('system_settings') }}" class="btn btn-success">
                    <i class="fas fa-cog me-2"></i>إعدادات النظام
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-users-cog fa-3x text-warning mb-3"></i>
                <h5>إدارة المستخدمين</h5>
                <p class="text-muted">إضافة وتعديل وحذف المستخدمين</p>
                <a href="{{ url_for('users') }}" class="btn btn-warning">
                    <i class="fas fa-users me-2"></i>إدارة المستخدمين
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Overview -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>نظرة سريعة على الإعدادات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-tags fa-2x text-primary mb-2"></i>
                            <h6>قواعد التسعير</h6>
                            <span class="badge bg-primary">{{ pricing_rules|length if pricing_rules else 0 }}</span>
                            <small class="text-muted d-block">قاعدة نشطة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-percentage fa-2x text-success mb-2"></i>
                            <h6>الضريبة</h6>
                            <span class="badge bg-success">{{ settings.get('tax_percentage', 15) }}%</span>
                            <small class="text-muted d-block">ضريبة القيمة المضافة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h6>التسليم</h6>
                            <span class="badge bg-warning">{{ settings.get('default_delivery_days', 7) }} أيام</span>
                            <small class="text-muted d-block">التسليم العادي</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-bell fa-2x text-info mb-2"></i>
                            <h6>الإشعارات</h6>
                            <span class="badge bg-info">
                                {% if settings.get('sms_notifications', True) %}مفعلة{% else %}معطلة{% endif %}
                            </span>
                            <small class="text-muted d-block">رسائل SMS</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Pricing Rules -->
{% if pricing_rules %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-list me-2"></i>قواعد التسعير الحديثة</h6>
                <a href="{{ url_for('pricing_settings') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>نوع القماش</th>
                                <th>السعر الأساسي</th>
                                <th>مضاعف العاجل</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rule in pricing_rules[:5] %}
                            <tr>
                                <td>{{ rule.fabric_type }}</td>
                                <td>{{ "%.2f"|format(rule.base_price) }} ريال</td>
                                <td>{{ "%.1f"|format(rule.rush_multiplier) }}x</td>
                                <td>
                                    {% if rule.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">معطل</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="row">
    <!-- Company Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i>إعدادات الشركة</h5>
            </div>
            <div class="card-body">
                <form id="companyForm">
                    <div class="mb-3">
                        <label class="form-label">اسم الشركة (عربي)</label>
                        <input type="text" class="form-control" value="نظام إدارة خياطة الرجال القطري">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اسم الشركة (إنجليزي)</label>
                        <input type="text" class="form-control" value="Qatari Men's Sewing Management System">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" rows="3">الدوحة، قطر</textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="text" class="form-control" value="+974 XXXX XXXX">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" placeholder="<EMAIL>">
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- System Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sliders-h me-2"></i>إعدادات النظام</h5>
            </div>
            <div class="card-body">
                <form id="systemForm">
                    <div class="mb-3">
                        <label class="form-label">عدد العناصر في الصفحة</label>
                        <select class="form-select">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اللغة الافتراضية</label>
                        <select class="form-select">
                            <option value="ar" selected>العربية</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">العملة</label>
                        <select class="form-select">
                            <option value="QAR" selected>ريال قطري (QAR)</option>
                            <option value="USD">دولار أمريكي (USD)</option>
                            <option value="EUR">يورو (EUR)</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                            <label class="form-check-label" for="autoBackup">
                                النسخ الاحتياطي التلقائي
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                            <label class="form-check-label" for="emailNotifications">
                                إشعارات البريد الإلكتروني
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Pricing Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-money-bill me-2"></i>إعدادات الأسعار</h5>
            </div>
            <div class="card-body">
                <form id="pricingForm">
                    <div class="mb-3">
                        <label class="form-label">سعر الثوب العادي (ريال)</label>
                        <input type="number" class="form-control" value="300" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سعر الثوب الفاخر (ريال)</label>
                        <input type="number" class="form-control" value="500" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سعر ثوب الحرير (ريال)</label>
                        <input type="number" class="form-control" value="700" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رسوم التعديل (ريال)</label>
                        <input type="number" class="form-control" value="50" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">خصم العملاء المميزين (%)</label>
                        <input type="number" class="form-control" value="10" min="0" max="50">
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Backup & Maintenance -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tools me-2"></i>النسخ الاحتياطي والصيانة</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">آخر نسخة احتياطية</label>
                    <p class="text-muted">{{ now.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="createBackup()">
                        <i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية
                    </button>
                    <button class="btn btn-outline-warning" onclick="clearCache()">
                        <i class="fas fa-broom me-2"></i>مسح الذاكرة المؤقتة
                    </button>
                    <button class="btn btn-outline-info" onclick="checkUpdates()">
                        <i class="fas fa-sync me-2"></i>فحص التحديثات
                    </button>
                    <button class="btn btn-outline-danger" onclick="resetSystem()">
                        <i class="fas fa-redo me-2"></i>إعادة تعيين النظام
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>إصدار النظام:</strong><br>
                        <span class="text-muted">1.0.0</span>
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ التثبيت:</strong><br>
                        <span class="text-muted">{{ now.strftime('%Y-%m-%d') }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>قاعدة البيانات:</strong><br>
                        <span class="text-muted">SQLite</span>
                    </div>
                    <div class="col-md-3">
                        <strong>حجم قاعدة البيانات:</strong><br>
                        <span class="text-muted">~2 MB</span>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-3">
                        <strong>إجمالي العملاء:</strong><br>
                        <span class="text-primary">5</span>
                    </div>
                    <div class="col-md-3">
                        <strong>إجمالي الطلبات:</strong><br>
                        <span class="text-success">6</span>
                    </div>
                    <div class="col-md-3">
                        <strong>إجمالي المستخدمين:</strong><br>
                        <span class="text-info">2</span>
                    </div>
                    <div class="col-md-3">
                        <strong>مساحة التخزين:</strong><br>
                        <span class="text-warning">~10 MB</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function saveSettings() {
    // Placeholder for save functionality
    alert('تم حفظ الإعدادات بنجاح!');
}

function createBackup() {
    // Placeholder for backup functionality
    alert('تم إنشاء النسخة الاحتياطية بنجاح!');
}

function clearCache() {
    // Placeholder for cache clearing
    alert('تم مسح الذاكرة المؤقتة بنجاح!');
}

function checkUpdates() {
    // Placeholder for update checking
    alert('النظام محدث إلى أحدث إصدار!');
}

function resetSystem() {
    if (confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم فقدان جميع البيانات!')) {
        alert('تم إلغاء العملية للحماية!');
    }
}

// Auto-save settings on change
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('change', function() {
            console.log('Settings changed - auto-save triggered');
        });
    });
});
</script>
{% endblock %}
