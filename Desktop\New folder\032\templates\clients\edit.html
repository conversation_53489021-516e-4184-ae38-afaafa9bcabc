{% extends "base.html" %}

{% block title %}تعديل {{ client.full_name }} - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-edit me-2"></i>تعديل بيانات العميل</h2>
    <div>
        <a href="{{ url_for('view_client', id=client.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للعميل
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>تعديل بيانات {{ client.full_name }}</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                            {% if form.full_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.full_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.mobile_number.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                {{ form.mobile_number(class="form-control" + (" is-invalid" if form.mobile_number.errors else "")) }}
                            </div>
                            {% if form.mobile_number.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.mobile_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>



                    <div class="mb-3">
                        {{ form.address.label(class="form-label") }}
                        {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else ""), rows="3") }}
                        {% if form.address.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">العنوان اختياري</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('view_client', id=client.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Client Summary -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>ملخص العميل</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>تاريخ التسجيل:</strong> {{ client.created_at.strftime('%Y-%m-%d %H:%M') }}<br>
                            <strong>آخر تحديث:</strong> {{ client.updated_at.strftime('%Y-%m-%d %H:%M') }}<br>
                            <strong>عدد المقاسات:</strong> {{ client.measurements|length }}<br>
                            <strong>عدد الطلبات:</strong> {{ client.orders|length }}
                        </small>
                    </div>
                    <div class="col-md-6">
                        {% if client.orders %}
                        <small class="text-muted">
                            <strong>آخر طلب:</strong> {{ client.orders[0].order_date.strftime('%Y-%m-%d') }}<br>
                            <strong>إجمالي المبلغ:</strong> {{ "%.2f"|format(client.orders|sum(attribute='total_price')) }} ريال
                        </small>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-format phone number
document.getElementById('mobile_number').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 0) {
        if (!value.startsWith('974') && !value.startsWith('+974')) {
            if (value.length <= 8) {
                value = '974' + value;
            }
        }
    }
    e.target.value = value;
});

// Focus on first input
document.getElementById('full_name').focus();
</script>
{% endblock %}
