{% extends "base.html" %}

{% block title %}إعدادات النظام - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-cogs me-2"></i>إعدادات النظام</h2>
    <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للإعدادات
    </a>
</div>

<form method="POST">
    {{ form.hidden_tag() }}

    <div class="row">
        <!-- Company Settings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>إعدادات الشركة</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        {{ form.company_name.label(class="form-label") }}
                        {{ form.company_name(class="form-control") }}
                    </div>

                    <div class="mb-3">
                        {{ form.company_address.label(class="form-label") }}
                        {{ form.company_address(class="form-control", rows="3") }}
                    </div>

                    <div class="mb-3">
                        {{ form.company_phone.label(class="form-label") }}
                        {{ form.company_phone(class="form-control") }}
                    </div>

                    <div class="mb-3">
                        {{ form.company_email.label(class="form-label") }}
                        {{ form.company_email(class="form-control") }}
                    </div>

                    <div class="d-grid">
                        <button type="button" class="btn btn-primary" onclick="saveCompanySettings()">
                            <i class="fas fa-save me-2"></i>حفظ إعدادات الشركة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pricing Settings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i>إعدادات التسعير</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        {{ form.default_delivery_days.label(class="form-label") }}
                        <div class="input-group">
                            {{ form.default_delivery_days(class="form-control") }}
                            <span class="input-group-text">يوم</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.rush_order_days.label(class="form-label") }}
                        <div class="input-group">
                            {{ form.rush_order_days(class="form-control") }}
                            <span class="input-group-text">يوم</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.minimum_order_amount.label(class="form-label") }}
                        <div class="input-group">
                            {{ form.minimum_order_amount(class="form-control") }}
                            <span class="input-group-text">ريال</span>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="button" class="btn btn-success" onclick="savePricingSettings()">
                            <i class="fas fa-save me-2"></i>حفظ إعدادات التسعير
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Discount Settings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-percentage me-2"></i>إعدادات الخصومات</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        {{ form.bulk_discount_threshold.label(class="form-label") }}
                        <div class="input-group">
                            {{ form.bulk_discount_threshold(class="form-control") }}
                            <span class="input-group-text">قطعة</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.bulk_discount_percentage.label(class="form-label") }}
                        <div class="input-group">
                            {{ form.bulk_discount_percentage(class="form-control") }}
                            <span class="input-group-text">%</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.vip_discount_percentage.label(class="form-label") }}
                        <div class="input-group">
                            {{ form.vip_discount_percentage(class="form-control") }}
                            <span class="input-group-text">%</span>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="button" class="btn btn-warning" onclick="saveDiscountSettings()">
                            <i class="fas fa-save me-2"></i>حفظ إعدادات الخصومات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tax Settings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>إعدادات الضريبة</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.tax_enabled(class="form-check-input") }}
                            {{ form.tax_enabled.label(class="form-check-label") }}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.tax_percentage.label(class="form-label") }}
                        <div class="input-group">
                            {{ form.tax_percentage(class="form-control") }}
                            <span class="input-group-text">%</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.tax_number.label(class="form-label") }}
                        {{ form.tax_number(class="form-control") }}
                        <div class="form-text">الرقم الضريبي للشركة</div>
                    </div>

                    <div class="d-grid">
                        <button type="button" class="btn btn-info" onclick="saveTaxSettings()">
                            <i class="fas fa-save me-2"></i>حفظ إعدادات الضريبة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Notification Settings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bell me-2"></i>إعدادات الإشعارات</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.sms_notifications(class="form-check-input") }}
                            {{ form.sms_notifications.label(class="form-check-label") }}
                        </div>
                        <div class="form-text">إرسال رسائل نصية للعملاء</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.email_notifications(class="form-check-input") }}
                            {{ form.email_notifications.label(class="form-check-label") }}
                        </div>
                        <div class="form-text">إرسال إيميلات للعملاء</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.whatsapp_notifications(class="form-check-input") }}
                            {{ form.whatsapp_notifications.label(class="form-check-label") }}
                        </div>
                        <div class="form-text">إرسال رسائل واتساب (قريباً)</div>
                    </div>

                    <div class="d-grid">
                        <button type="button" class="btn btn-secondary" onclick="saveNotificationSettings()">
                            <i class="fas fa-save me-2"></i>حفظ إعدادات الإشعارات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Settings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-server me-2"></i>إعدادات النظام</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.auto_backup(class="form-check-input") }}
                            {{ form.auto_backup.label(class="form-check-label") }}
                        </div>
                        <div class="form-text">نسخ احتياطي تلقائي للبيانات</div>
                    </div>

                    <div class="mb-3">
                        {{ form.backup_frequency.label(class="form-label") }}
                        {{ form.backup_frequency(class="form-select") }}
                    </div>

                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle me-2"></i>
                            النسخ الاحتياطي يحفظ جميع البيانات في مجلد منفصل
                        </small>
                    </div>

                    <div class="d-grid">
                        <button type="button" class="btn btn-dark" onclick="saveSystemSettings()">
                            <i class="fas fa-save me-2"></i>حفظ إعدادات النظام
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Save All Button -->
    <div class="row">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white text-center">
                    <h6 class="mb-0"><i class="fas fa-save me-2"></i>حفظ شامل</h6>
                </div>
                <div class="card-body text-center">
                    <p class="text-muted mb-3">
                        يمكنك حفظ كل قسم على حدة أو حفظ جميع الإعدادات مرة واحدة
                    </p>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>حفظ جميع الإعدادات
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg" onclick="resetAllSettings()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            سيتم تطبيق الإعدادات فوراً على النظام
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Settings Preview -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-eye me-2"></i>معاينة الإعدادات</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>التسليم:</h6>
                        <small class="text-muted">
                            عادي: <span id="preview-delivery">7</span> أيام<br>
                            عاجل: <span id="preview-rush">3</span> أيام
                        </small>
                    </div>
                    <div class="col-md-3">
                        <h6>الخصومات:</h6>
                        <small class="text-muted">
                            جماعي: <span id="preview-bulk">10</span>% للـ<span id="preview-threshold">5</span>+ قطع<br>
                            VIP: <span id="preview-vip">15</span>%
                        </small>
                    </div>
                    <div class="col-md-3">
                        <h6>الضريبة:</h6>
                        <small class="text-muted">
                            نسبة: <span id="preview-tax">15</span>%<br>
                            حالة: <span id="preview-tax-status">مفعلة</span>
                        </small>
                    </div>
                    <div class="col-md-3">
                        <h6>الحد الأدنى:</h6>
                        <small class="text-muted">
                            <span id="preview-minimum">50</span> ريال
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update preview when values change
    function updatePreview() {
        document.getElementById('preview-delivery').textContent =
            document.getElementById('default_delivery_days').value || '7';
        document.getElementById('preview-rush').textContent =
            document.getElementById('rush_order_days').value || '3';
        document.getElementById('preview-bulk').textContent =
            document.getElementById('bulk_discount_percentage').value || '10';
        document.getElementById('preview-threshold').textContent =
            document.getElementById('bulk_discount_threshold').value || '5';
        document.getElementById('preview-vip').textContent =
            document.getElementById('vip_discount_percentage').value || '15';
        document.getElementById('preview-tax').textContent =
            document.getElementById('tax_percentage').value || '15';
        document.getElementById('preview-minimum').textContent =
            document.getElementById('minimum_order_amount').value || '50';
        document.getElementById('preview-tax-status').textContent =
            document.getElementById('tax_enabled').checked ? 'مفعلة' : 'معطلة';
    }

    // Add event listeners to all inputs
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('input', updatePreview);
        input.addEventListener('change', updatePreview);
    });

    // Initial preview update
    updatePreview();

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const deliveryDays = parseInt(document.getElementById('default_delivery_days').value);
        const rushDays = parseInt(document.getElementById('rush_order_days').value);

        if (rushDays >= deliveryDays) {
            e.preventDefault();
            alert('أيام الطلب العاجل يجب أن تكون أقل من أيام التسليم العادي');
            return false;
        }

        return true;
    });
});

// Individual save functions
function saveCompanySettings() {
    const formData = new FormData();
    formData.append('csrf_token', document.querySelector('[name=csrf_token]').value);
    formData.append('company_name', document.getElementById('company_name').value);
    formData.append('company_address', document.getElementById('company_address').value);
    formData.append('company_phone', document.getElementById('company_phone').value);
    formData.append('company_email', document.getElementById('company_email').value);
    formData.append('section', 'company');

    saveSettings(formData, 'إعدادات الشركة');
}

function savePricingSettings() {
    const formData = new FormData();
    formData.append('csrf_token', document.querySelector('[name=csrf_token]').value);
    formData.append('default_delivery_days', document.getElementById('default_delivery_days').value);
    formData.append('rush_order_days', document.getElementById('rush_order_days').value);
    formData.append('minimum_order_amount', document.getElementById('minimum_order_amount').value);
    formData.append('section', 'pricing');

    saveSettings(formData, 'إعدادات التسعير');
}

function saveDiscountSettings() {
    const formData = new FormData();
    formData.append('csrf_token', document.querySelector('[name=csrf_token]').value);
    formData.append('bulk_discount_threshold', document.getElementById('bulk_discount_threshold').value);
    formData.append('bulk_discount_percentage', document.getElementById('bulk_discount_percentage').value);
    formData.append('vip_discount_percentage', document.getElementById('vip_discount_percentage').value);
    formData.append('section', 'discount');

    saveSettings(formData, 'إعدادات الخصومات');
}

function saveTaxSettings() {
    const formData = new FormData();
    formData.append('csrf_token', document.querySelector('[name=csrf_token]').value);
    formData.append('tax_enabled', document.getElementById('tax_enabled').checked);
    formData.append('tax_percentage', document.getElementById('tax_percentage').value);
    formData.append('tax_number', document.getElementById('tax_number').value);
    formData.append('section', 'tax');

    saveSettings(formData, 'إعدادات الضريبة');
}

function saveNotificationSettings() {
    const formData = new FormData();
    formData.append('csrf_token', document.querySelector('[name=csrf_token]').value);
    formData.append('sms_notifications', document.getElementById('sms_notifications').checked);
    formData.append('email_notifications', document.getElementById('email_notifications').checked);
    formData.append('whatsapp_notifications', document.getElementById('whatsapp_notifications').checked);
    formData.append('section', 'notifications');

    saveSettings(formData, 'إعدادات الإشعارات');
}

function saveSystemSettings() {
    const formData = new FormData();
    formData.append('csrf_token', document.querySelector('[name=csrf_token]').value);
    formData.append('auto_backup', document.getElementById('auto_backup').checked);
    formData.append('backup_frequency', document.getElementById('backup_frequency').value);
    formData.append('section', 'system');

    saveSettings(formData, 'إعدادات النظام');
}

function saveSettings(formData, sectionName) {
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    button.disabled = true;

    fetch('{{ url_for("system_settings") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showNotification('تم حفظ ' + sectionName + ' بنجاح', 'success');

            // Update button to show success
            button.innerHTML = '<i class="fas fa-check me-2"></i>تم الحفظ';
            button.className = button.className.replace(/btn-\w+/, 'btn-success');

            // Reset button after 2 seconds
            setTimeout(() => {
                button.innerHTML = originalText;
                button.className = button.className.replace('btn-success', getOriginalButtonClass(button));
                button.disabled = false;
            }, 2000);
        } else {
            throw new Error(data.message || 'حدث خطأ أثناء الحفظ');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('خطأ في حفظ ' + sectionName + ': ' + error.message, 'error');

        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function getOriginalButtonClass(button) {
    if (button.onclick.toString().includes('saveCompanySettings')) return 'btn-primary';
    if (button.onclick.toString().includes('savePricingSettings')) return 'btn-success';
    if (button.onclick.toString().includes('saveDiscountSettings')) return 'btn-warning';
    if (button.onclick.toString().includes('saveTaxSettings')) return 'btn-info';
    if (button.onclick.toString().includes('saveNotificationSettings')) return 'btn-secondary';
    if (button.onclick.toString().includes('saveSystemSettings')) return 'btn-dark';
    return 'btn-primary';
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function resetAllSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        location.reload();
    }
}
</script>
{% endblock %}
