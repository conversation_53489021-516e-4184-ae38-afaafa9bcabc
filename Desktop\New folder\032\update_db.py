#!/usr/bin/env python3
"""
Database update script to add new measurement columns
"""

from app import app, db
import sqlite3

def update_database():
    """Update the database with new measurement columns"""
    
    # Connect directly to SQLite database
    conn = sqlite3.connect('tailoring_system.db')
    cursor = conn.cursor()
    
    # List of new columns to add
    new_columns = [
        ('height', 'FLOAT'),
        ('chest', 'FLOAT'),
        ('waist', 'FLOAT'),
        ('hip', 'FLOAT'),
        ('sleeve_length', 'FLOAT'),
        ('arm_circumference', 'FLOAT'),
        ('thobe_length', 'FLOAT'),
        ('bisht_length', 'FLOAT'),
        ('back_width', 'FLOAT'),
        ('front_length', 'FLOAT'),
        ('back_length', 'FLOAT'),
        ('sleeve_opening', 'FLOAT'),
        ('armpit_depth', 'FLOAT'),
        ('hem_width', 'FLOAT'),
        ('pocket_position', 'FLOAT'),
        ('has_collar', 'BOOLEAN DEFAULT 0'),
        ('has_cuffs', 'BOOLEAN DEFAULT 0'),
        ('garment_type', 'VARCHAR(50)'),
        ('style_preference', 'VARCHAR(50)'),
        ('special_instructions', 'TEXT')
    ]
    
    # Check existing columns
    cursor.execute("PRAGMA table_info(measurements)")
    existing_columns = [row[1] for row in cursor.fetchall()]
    print(f"Existing columns: {existing_columns}")
    
    # Add new columns
    for column_name, column_type in new_columns:
        if column_name not in existing_columns:
            try:
                sql = f"ALTER TABLE measurements ADD COLUMN {column_name} {column_type}"
                cursor.execute(sql)
                print(f"✅ Added column: {column_name}")
            except Exception as e:
                print(f"❌ Error adding {column_name}: {e}")
        else:
            print(f"⚠️ Column {column_name} already exists")
    
    # Commit changes
    conn.commit()
    conn.close()
    print("\n🎉 Database update completed!")

if __name__ == "__main__":
    update_database()
