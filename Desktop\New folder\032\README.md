# نظام إدارة خياطة الرجال القطري
## Qatari Men's Sewing Management System

نظام شامل لإدارة ورشة خياطة الرجال القطرية باستخدام Flask و Python.

## المميزات الرئيسية

### 🏪 إدارة العملاء
- إضافة وتعديل بيانات العملاء
- حفظ معلومات الاتصال والعنوان
- تتبع تاريخ التسجيل والتحديثات

### 📏 نظام المقاسات
- تسجيل مقاسات مفصلة للثوب القطري
- دليل بصري للمقاسات مع الرسم التوضيحي
- حفظ أنواع الأقمشة والملاحظات الخاصة
- دعم خاصية الزنجفاش

### 📋 إدارة الطلبات
- تسجيل طلبات جديدة مع تفاصيل كاملة
- تتبع حالة الطلب (جديد - قيد التنفيذ - جاهز - تم التسليم)
- رفع صور الأقمشة والطلبات الخاصة
- حساب الأسعار وتواريخ التسليم

### 🧾 نظام الفواتير
- إنشاء فواتير PDF باللغة العربية
- تصميم احترافي مع معلومات كاملة
- دعم النصوص العربية بشكل صحيح

### 📊 التقارير والإحصائيات
- تقارير يومية وشهرية
- إحصائيات المبيعات والإيرادات
- تحليل أنواع الأقمشة الأكثر طلباً
- قائمة العملاء الأكثر نشاطاً

### 👥 إدارة المستخدمين
- نظام تسجيل دخول آمن
- أدوار مختلفة (مدير - مشغل - مشاهد)
- تتبع آخر دخول للمستخدمين

## متطلبات النظام

```
Python 3.8+
Flask >= 2.3.0
Flask-SQLAlchemy >= 3.0.0
Flask-Login >= 0.6.0
Flask-WTF >= 1.1.0
WTForms >= 3.0.0
Flask-Migrate >= 4.0.0
ReportLab >= 4.0.0 (للفواتير)
python-bidi >= 0.4.0 (للنصوص العربية)
arabic-reshaper >= 3.0.0 (للنصوص العربية)
```

## التثبيت والتشغيل

### 1. تحميل المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python app.py
```

### 3. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost:5050`

### 4. تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## هيكل قاعدة البيانات

### جدول العملاء (clients)
- الرقم التعريفي
- الاسم الكامل
- رقم الجوال
- رقم البطاقة
- العنوان
- تاريخ التسجيل

### جدول المقاسات (measurements)
- رقم العميل
- محيط الرقبة
- عرض الكتف
- طول الكم
- طول الثوب
- عرض الصدر
- طول السحاب
- زنجفاش (نعم/لا)
- عرض الأسفل
- نوع القماش

### جدول الطلبات (orders)
- رقم العميل
- تاريخ الطلب
- نوع القماش
- عدد القطع
- السعر الإجمالي
- الحالة
- تاريخ التسليم المتوقع
- ملاحظات

## الواجهات الرئيسية

### 🏠 لوحة التحكم
- إحصائيات سريعة
- الطلبات الأخيرة
- إجراءات سريعة
- رسوم بيانية

### 👥 إدارة العملاء
- قائمة العملاء مع البحث
- إضافة عميل جديد
- عرض تفاصيل العميل
- تعديل بيانات العميل

### 📏 تسجيل المقاسات
- واجهة تفاعلية مع رسم توضيحي
- حقول مقاسات مفصلة
- حفظ أنواع الأقمشة
- ملاحظات خاصة

### 📋 إدارة الطلبات
- قائمة الطلبات مع الفلترة
- إضافة طلب جديد
- تحديث حالة الطلب
- طباعة الفواتير

## المميزات التقنية

### 🎨 التصميم
- واجهة عربية بتقنية RTL
- تصميم متجاوب (Responsive)
- Bootstrap 5 مع دعم العربية
- أيقونات Font Awesome

### 🔒 الأمان
- تشفير كلمات المرور
- جلسات آمنة
- حماية من CSRF
- تحكم في الصلاحيات

### 📱 سهولة الاستخدام
- واجهة بديهية
- رسائل تأكيد واضحة
- تنقل سهل
- بحث وفلترة متقدمة

## الدعم والتطوير

### إضافة مميزات جديدة
- نظام المخزون
- تتبع المواد الخام
- تقارير محاسبية متقدمة
- تطبيق جوال

### التخصيص
- إعدادات الشركة
- قوالب فواتير مخصصة
- أنواع أقمشة إضافية
- حقول مقاسات جديدة

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**تم التطوير بواسطة:** Augment Agent
**التاريخ:** 2024
**الإصدار:** 1.0.0
