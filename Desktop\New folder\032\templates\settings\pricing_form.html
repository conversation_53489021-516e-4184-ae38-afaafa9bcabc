{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tag me-2"></i>{{ title }}</h2>
    <a href="{{ url_for('pricing_settings') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة لإعدادات الأسعار
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags me-2"></i>
                    {% if pricing_rule %}
                        تعديل قاعدة تسعير: {{ pricing_rule.fabric_type }}
                    {% else %}
                        إضافة قاعدة تسعير جديدة
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.fabric_type.label(class="form-label") }}
                            {{ form.fabric_type(class="form-control" + (" is-invalid" if form.fabric_type.errors else "")) }}
                            {% if form.fabric_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.fabric_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">مثال: ثوب صيفي، بشت فاخر، دشداشة</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.base_price.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.base_price(class="form-control" + (" is-invalid" if form.base_price.errors else "")) }}
                                <span class="input-group-text">ريال</span>
                            </div>
                            {% if form.base_price.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.base_price.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">السعر الأساسي للقطعة الواحدة</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.size_multiplier.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.size_multiplier(class="form-control" + (" is-invalid" if form.size_multiplier.errors else ""), step="0.1") }}
                                <span class="input-group-text">x</span>
                            </div>
                            {% if form.size_multiplier.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.size_multiplier.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">مضاعف حسب حجم العميل (1.0 = عادي)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.rush_multiplier.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.rush_multiplier(class="form-control" + (" is-invalid" if form.rush_multiplier.errors else ""), step="0.1") }}
                                <span class="input-group-text">x</span>
                            </div>
                            {% if form.rush_multiplier.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.rush_multiplier.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">مضاعف للطلبات العاجلة (1.5 = 50% زيادة)</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        <div class="form-text">قواعد التسعير غير النشطة لن تظهر في الحاسبة</div>
                    </div>
                    
                    <!-- Price Preview -->
                    <div class="alert alert-light border">
                        <h6><i class="fas fa-eye me-2"></i>معاينة التسعير:</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <small class="text-muted">سعر عادي:</small><br>
                                <span id="normal-price" class="text-success">0.00 ريال</span>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">سعر عاجل:</small><br>
                                <span id="rush-price" class="text-warning">0.00 ريال</span>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">مع الضريبة:</small><br>
                                <span id="tax-price" class="text-info">0.00 ريال</span>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">عاجل + ضريبة:</small><br>
                                <span id="rush-tax-price" class="text-danger">0.00 ريال</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('pricing_settings') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% if pricing_rule %}
                                تحديث القاعدة
                            {% else %}
                                حفظ القاعدة
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>مساعدة في التسعير</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>أمثلة على أنواع الأقمشة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>ثوب صيفي قطني</li>
                            <li><i class="fas fa-check text-success me-2"></i>ثوب شتوي صوفي</li>
                            <li><i class="fas fa-check text-success me-2"></i>بشت عادي</li>
                            <li><i class="fas fa-check text-success me-2"></i>بشت فاخر مطرز</li>
                            <li><i class="fas fa-check text-success me-2"></i>دشداشة كويتية</li>
                            <li><i class="fas fa-check text-success me-2"></i>جلابية مصرية</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>نصائح للتسعير:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>احسب تكلفة المواد</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>اعتبر وقت العمل</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>أضف هامش ربح مناسب</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>راجع أسعار المنافسين</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>قدم خصومات للكميات</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>احسب تكلفة التطريز</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const basePriceInput = document.getElementById('base_price');
    const sizeMultiplierInput = document.getElementById('size_multiplier');
    const rushMultiplierInput = document.getElementById('rush_multiplier');
    
    function updatePreview() {
        const basePrice = parseFloat(basePriceInput.value) || 0;
        const sizeMultiplier = parseFloat(sizeMultiplierInput.value) || 1;
        const rushMultiplier = parseFloat(rushMultiplierInput.value) || 1.5;
        
        const normalPrice = basePrice * sizeMultiplier;
        const rushPrice = normalPrice * rushMultiplier;
        const taxPrice = normalPrice * 1.15;
        const rushTaxPrice = rushPrice * 1.15;
        
        document.getElementById('normal-price').textContent = normalPrice.toFixed(2) + ' ريال';
        document.getElementById('rush-price').textContent = rushPrice.toFixed(2) + ' ريال';
        document.getElementById('tax-price').textContent = taxPrice.toFixed(2) + ' ريال';
        document.getElementById('rush-tax-price').textContent = rushTaxPrice.toFixed(2) + ' ريال';
    }
    
    // Event listeners
    basePriceInput.addEventListener('input', updatePreview);
    sizeMultiplierInput.addEventListener('input', updatePreview);
    rushMultiplierInput.addEventListener('input', updatePreview);
    
    // Initial preview
    updatePreview();
    
    // Focus on first input
    document.getElementById('fabric_type').focus();
});
</script>
{% endblock %}
