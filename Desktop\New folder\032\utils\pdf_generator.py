try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_RIGHT, TA_CENTER
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

from datetime import datetime
import os

def setup_arabic_font():
    """Setup Arabic font for PDF generation"""
    try:
        # Try to register Arabic font (you may need to download and place an Arabic font file)
        # For now, we'll use the default font with Arabic reshaping
        pass
    except:
        pass

def reshape_arabic_text(text):
    """Reshape Arabic text for proper display in PDF"""
    if not text:
        return ""
    if ARABIC_SUPPORT:
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return text
    return text

def generate_invoice_pdf(order, filename=None):
    """Generate PDF invoice for an order"""
    if not REPORTLAB_AVAILABLE:
        raise Exception("ReportLab is not installed. Cannot generate PDF.")

    if not filename:
        filename = f"invoice_{order.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

    filepath = os.path.join('static', 'invoices', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    # Create PDF document
    doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72,
                           topMargin=72, bottomMargin=18)

    # Container for the 'Flowable' objects
    elements = []

    # Define styles
    styles = getSampleStyleSheet()

    # Arabic title style
    title_style = ParagraphStyle(
        'ArabicTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=TA_CENTER,
        fontName='Helvetica-Bold'
    )

    # Arabic normal style
    arabic_style = ParagraphStyle(
        'Arabic',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=12,
        alignment=TA_RIGHT,
        fontName='Helvetica'
    )

    # Company header
    company_name = reshape_arabic_text("نظام إدارة خياطة الرجال القطري")
    elements.append(Paragraph(company_name, title_style))
    elements.append(Spacer(1, 12))

    # Invoice title
    invoice_title = reshape_arabic_text(f"فاتورة رقم: {order.id}")
    elements.append(Paragraph(invoice_title, title_style))
    elements.append(Spacer(1, 20))

    # Client information
    client_info = [
        [reshape_arabic_text("اسم العميل:"), reshape_arabic_text(order.client.full_name)],
        [reshape_arabic_text("رقم الجوال:"), order.client.mobile_number],
        [reshape_arabic_text("تاريخ الطلب:"), order.order_date.strftime('%Y-%m-%d')],
        [reshape_arabic_text("تاريخ التسليم المتوقع:"),
         order.expected_delivery.strftime('%Y-%m-%d') if order.expected_delivery else reshape_arabic_text("غير محدد")]
    ]

    client_table = Table(client_info, colWidths=[2*inch, 3*inch])
    client_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    elements.append(client_table)
    elements.append(Spacer(1, 20))

    # Order details
    order_details = [
        [reshape_arabic_text("البند"), reshape_arabic_text("التفاصيل")],
        [reshape_arabic_text("نوع القماش"), reshape_arabic_text(order.fabric_type)],
        [reshape_arabic_text("عدد القطع"), str(order.pieces_count)],
        [reshape_arabic_text("السعر الإجمالي"), f"{order.total_price} ريال"],
        [reshape_arabic_text("الحالة"), reshape_arabic_text(order.get_status_display())]
    ]

    order_table = Table(order_details, colWidths=[2*inch, 3*inch])
    order_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
    ]))

    elements.append(order_table)
    elements.append(Spacer(1, 30))

    # Footer
    footer_text = reshape_arabic_text("شكراً لتعاملكم معنا")
    footer_style = ParagraphStyle(
        'Footer',
        parent=styles['Normal'],
        fontSize=14,
        alignment=TA_CENTER,
        fontName='Helvetica-Bold'
    )
    elements.append(Paragraph(footer_text, footer_style))

    # Build PDF
    doc.build(elements)

    return filepath
