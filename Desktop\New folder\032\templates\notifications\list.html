{% extends "base.html" %}

{% block title %}الإشعارات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bell me-2"></i>الإشعارات</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">الإشعارات</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Notification Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">
                                <i class="fas fa-filter me-2"></i>تصفية الإشعارات
                                {% if unread_count > 0 %}
                                <span class="badge bg-danger">{{ unread_count }} غير مقروء</span>
                                {% endif %}
                            </h6>
                        </div>
                        <div class="btn-group">
                            <a href="{{ url_for('notifications') }}" 
                               class="btn btn-outline-primary {{ 'active' if not unread_only else '' }}">
                                <i class="fas fa-list me-2"></i>جميع الإشعارات
                            </a>
                            <a href="{{ url_for('notifications', unread=True) }}" 
                               class="btn btn-outline-warning {{ 'active' if unread_only else '' }}">
                                <i class="fas fa-exclamation-circle me-2"></i>غير المقروءة
                            </a>
                            {% if unread_count > 0 %}
                            <button type="button" class="btn btn-outline-success" onclick="markAllAsRead()">
                                <i class="fas fa-check-double me-2"></i>تمييز الكل كمقروء
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="row">
        <div class="col-12">
            {% if notifications.items %}
                {% for notification in notifications.items %}
                <div class="card mb-3 notification-card {{ 'unread' if not notification.is_read else '' }}" 
                     data-notification-id="{{ notification.id }}">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon me-3">
                                <i class="{{ notification.get_icon() }} text-{{ notification.get_color() }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="notification-title mb-1">
                                            {{ notification.title }}
                                            {% if not notification.is_read %}
                                            <span class="badge bg-primary">جديد</span>
                                            {% endif %}
                                        </h6>
                                        <p class="notification-message mb-2">{{ notification.message }}</p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                                            
                                            {% if notification.category %}
                                            <span class="badge bg-secondary ms-2">{{ notification.category }}</span>
                                            {% endif %}
                                        </small>
                                    </div>
                                    <div class="notification-actions">
                                        {% if not notification.is_read %}
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="markAsRead({{ notification.id }})">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        {% endif %}
                                        
                                        {% if notification.order_id %}
                                        <a href="{{ url_for('view_order', id=notification.order_id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% endif %}
                                        
                                        {% if notification.client_id %}
                                        <a href="{{ url_for('view_client', id=notification.client_id) }}" 
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-user"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                
                <!-- Pagination -->
                {% if notifications.pages > 1 %}
                <nav aria-label="تصفح الإشعارات">
                    <ul class="pagination justify-content-center">
                        {% if notifications.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('notifications', page=notifications.prev_num, unread=unread_only) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in notifications.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != notifications.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('notifications', page=page_num, unread=unread_only) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if notifications.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('notifications', page=notifications.next_num, unread=unread_only) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
            {% else %}
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">
                            {% if unread_only %}
                                لا توجد إشعارات غير مقروءة
                            {% else %}
                                لا توجد إشعارات
                            {% endif %}
                        </h5>
                        <p class="text-muted">
                            {% if unread_only %}
                                جميع إشعاراتك مقروءة!
                            {% else %}
                                ستظهر الإشعارات هنا عند توفرها
                            {% endif %}
                        </p>
                        {% if unread_only %}
                        <a href="{{ url_for('notifications') }}" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>عرض جميع الإشعارات
                        </a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.notification-card {
    border-left: 4px solid #dee2e6;
    transition: all 0.3s ease;
}

.notification-card.unread {
    border-left-color: #007bff;
    background-color: #f8f9ff;
}

.notification-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.notification-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.notification-title {
    font-weight: 600;
    color: #495057;
}

.notification-message {
    color: #6c757d;
    line-height: 1.5;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
}

.notification-actions .btn {
    padding: 0.25rem 0.5rem;
}
</style>

<script>
function markAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrf_token]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const card = document.querySelector(`[data-notification-id="${notificationId}"]`);
            card.classList.remove('unread');
            card.querySelector('.badge').remove();
            card.querySelector('.btn-outline-success').remove();
            
            // Update unread count
            updateUnreadCount();
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function markAllAsRead() {
    if (confirm('هل تريد تمييز جميع الإشعارات كمقروءة؟')) {
        fetch('/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrf_token]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
}

function updateUnreadCount() {
    fetch('/api/notifications/unread-count')
    .then(response => response.json())
    .then(data => {
        const badge = document.querySelector('.badge.bg-danger');
        if (badge) {
            if (data.count > 0) {
                badge.textContent = `${data.count} غير مقروء`;
            } else {
                badge.remove();
            }
        }
    });
}
</script>
{% endblock %}
