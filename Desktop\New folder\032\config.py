import os
from datetime import timedelta

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'qatari-sewing-system-secret-key-2024'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///sewing_system.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Upload settings
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # Session settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    
    # Pagination
    ITEMS_PER_PAGE = 20
    
    # Default admin user
    DEFAULT_ADMIN_USERNAME = 'admin'
    DEFAULT_ADMIN_PASSWORD = 'admin123'
    
    # Arabic/RTL support
    LANGUAGES = ['ar', 'en']
    DEFAULT_LANGUAGE = 'ar'
    
    # Invoice settings
    COMPANY_NAME_AR = 'نظام إدارة خياطة الرجال القطري'
    COMPANY_NAME_EN = 'Qatari Men\'s Sewing Management System'
    COMPANY_ADDRESS_AR = 'الدوحة، قطر'
    COMPANY_PHONE = '+974 XXXX XXXX'
