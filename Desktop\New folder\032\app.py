from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
from datetime import datetime, date
import os
from config import Config
from models import db, User, Client, Measurement, Order, OrderStatusHistory, PricingRule, SystemSettings, Notification
from forms import LoginForm, ClientForm, MeasurementForm, OrderForm, OrderStatusForm, UserForm, SearchForm, PricingRuleForm, SystemSettingsForm, ProfileForm
from utils.pdf_generator import generate_invoice_pdf
from utils.reports import generate_daily_report, generate_monthly_report
from invoice_generator import ArabicInvoiceGenerator
from simple_arabic_invoice import SimpleArabicInvoiceGenerator

app = Flask(__name__)
app.config.from_object(Config)

# Initialize extensions
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Create upload directory
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Template context processors
@app.context_processor
def inject_now():
    return {'now': datetime.utcnow(), 'today': date.today()}

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data) and user.is_active:
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('auth/login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get statistics
    total_clients = Client.query.count()
    total_orders = Order.query.count()
    pending_orders = Order.query.filter(Order.status.in_(['new', 'ongoing'])).count()
    completed_orders = Order.query.filter_by(status='delivered').count()

    # Recent orders
    recent_orders = Order.query.order_by(Order.created_at.desc()).limit(5).all()

    # Monthly revenue
    current_month = datetime.now().month
    current_year = datetime.now().year
    monthly_revenue = db.session.query(db.func.sum(Order.total_price)).filter(
        db.extract('month', Order.order_date) == current_month,
        db.extract('year', Order.order_date) == current_year
    ).scalar() or 0

    return render_template('dashboard.html',
                         total_clients=total_clients,
                         total_orders=total_orders,
                         pending_orders=pending_orders,
                         completed_orders=completed_orders,
                         recent_orders=recent_orders,
                         monthly_revenue=monthly_revenue)

@app.route('/clients')
@login_required
def clients():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)

    query = Client.query
    if search:
        query = query.filter(Client.full_name.contains(search) |
                           Client.mobile_number.contains(search))

    clients = query.order_by(Client.created_at.desc()).paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False)

    return render_template('clients/list.html', clients=clients, search=search)

@app.route('/clients/add', methods=['GET', 'POST'])
@login_required
def add_client():
    form = ClientForm()
    if form.validate_on_submit():
        client = Client(
            full_name=form.full_name.data,
            mobile_number=form.mobile_number.data,
            address=form.address.data
        )
        db.session.add(client)
        db.session.commit()

        # Create notification for new client
        notification_title = "عميل جديد"
        notification_message = f"تم إضافة عميل جديد: {client.full_name} - {client.mobile_number}"

        # Notify all users
        notify_all_users(
            title=notification_title,
            message=notification_message,
            notification_type='info',
            category='client',
            client_id=client.id
        )

        flash('تم إضافة العميل بنجاح', 'success')
        return redirect(url_for('clients'))

    return render_template('clients/add.html', form=form)

@app.route('/clients/<int:id>')
@login_required
def view_client(id):
    client = Client.query.get_or_404(id)
    measurements = Measurement.query.filter_by(client_id=id).order_by(Measurement.created_at.desc()).all()
    orders = Order.query.filter_by(client_id=id).order_by(Order.created_at.desc()).all()

    return render_template('clients/view.html', client=client, measurements=measurements, orders=orders)

@app.route('/clients/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_client(id):
    client = Client.query.get_or_404(id)
    form = ClientForm(obj=client)

    if form.validate_on_submit():
        form.populate_obj(client)
        client.updated_at = datetime.utcnow()
        db.session.commit()

        # Create notification for client update
        notification_title = "تحديث بيانات عميل"
        notification_message = f"تم تحديث بيانات العميل: {client.full_name}"

        # Notify all users
        notify_all_users(
            title=notification_title,
            message=notification_message,
            notification_type='info',
            category='client',
            client_id=client.id
        )

        flash('تم تحديث بيانات العميل بنجاح', 'success')
        return redirect(url_for('view_client', id=id))

    return render_template('clients/edit.html', form=form, client=client)

@app.route('/measurements/<int:client_id>/add', methods=['GET', 'POST'])
@login_required
def add_measurement(client_id):
    client = Client.query.get_or_404(client_id)
    form = MeasurementForm()

    if form.validate_on_submit():
        measurement = Measurement(client_id=client_id)
        form.populate_obj(measurement)
        db.session.add(measurement)
        db.session.commit()

        # Create notification for new measurement
        notification_title = "إضافة مقاسات جديدة"
        notification_message = f"تم إضافة مقاسات جديدة للعميل {client.full_name} بواسطة {current_user.full_name}"

        # Notify all users except current user
        users = User.query.filter(User.is_active == True, User.id != current_user.id).all()
        for user in users:
            create_notification(
                user_id=user.id,
                title=notification_title,
                message=notification_message,
                notification_type='success',
                category='measurement',
                client_id=client_id
            )

        flash('تم حفظ المقاسات بنجاح', 'success')
        return redirect(url_for('view_client', id=client_id))

    return render_template('measurements/add.html', form=form, client=client)

@app.route('/measurements')
@login_required
def measurements():
    """View all measurements"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)

    query = Measurement.query.join(Client)
    if search:
        query = query.filter(Client.full_name.contains(search))

    measurements = query.order_by(Measurement.created_at.desc()).paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False)

    return render_template('measurements/list.html', measurements=measurements, search=search)

@app.route('/measurements/<int:id>')
@login_required
def view_measurement(id):
    """View measurement details"""
    measurement = Measurement.query.get_or_404(id)
    return render_template('measurements/view.html', measurement=measurement)

@app.route('/measurements/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_measurement(id):
    """Edit measurement"""
    measurement = Measurement.query.get_or_404(id)
    form = MeasurementForm(obj=measurement)

    if form.validate_on_submit():
        form.populate_obj(measurement)
        measurement.updated_at = datetime.utcnow()
        db.session.commit()

        # Create notification for measurement update
        notification_title = "تحديث مقاسات"
        notification_message = f"تم تحديث مقاسات العميل {measurement.client.full_name} بواسطة {current_user.full_name}"

        # Notify all users except current user
        users = User.query.filter(User.is_active == True, User.id != current_user.id).all()
        for user in users:
            create_notification(
                user_id=user.id,
                title=notification_title,
                message=notification_message,
                notification_type='info',
                category='measurement',
                client_id=measurement.client_id
            )

        flash('تم تحديث المقاسات بنجاح', 'success')
        return redirect(url_for('view_measurement', id=id))

    return render_template('measurements/edit.html', form=form, measurement=measurement)

@app.route('/orders')
@login_required
def orders():
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '', type=str)
    search = request.args.get('search', '', type=str)

    query = Order.query
    if status:
        if status == 'pending':
            query = query.filter(Order.status.in_(['new', 'ongoing']))
        else:
            query = query.filter_by(status=status)

    if search:
        query = query.join(Client).filter(Client.full_name.contains(search))

    orders = query.order_by(Order.created_at.desc()).paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False)

    return render_template('orders/list.html', orders=orders, status=status, search=search)

@app.route('/orders/add', methods=['GET', 'POST'])
@login_required
def add_order():
    form = OrderForm()
    form.client_id.choices = [(c.id, c.full_name) for c in Client.query.all()]

    if form.validate_on_submit():
        order = Order()
        form.populate_obj(order)

        # Calculate price automatically based on pricing rules
        calculated_price = order.calculate_price()
        if calculated_price > 0:
            order.total_price = calculated_price
        # If no pricing rule found, keep the manual price from form

        # Set expected delivery based on rush order setting
        if not order.expected_delivery:
            if order.is_rush_order:
                rush_days_setting = SystemSettings.query.filter_by(setting_key='rush_order_days').first()
                delivery_days = int(rush_days_setting.get_value()) if rush_days_setting else 3
            else:
                default_days_setting = SystemSettings.query.filter_by(setting_key='default_delivery_days').first()
                delivery_days = int(default_days_setting.get_value()) if default_days_setting else 7

            from datetime import timedelta
            order.expected_delivery = date.today() + timedelta(days=delivery_days)

        # Handle file upload
        if form.fabric_photo.data:
            filename = secure_filename(form.fabric_photo.data.filename)
            if filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                filename = timestamp + filename
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                form.fabric_photo.data.save(filepath)
                order.fabric_photo_path = filename

        db.session.add(order)
        db.session.commit()

        # Create notification for new order
        notification_title = f"طلب جديد #{order.id}"
        notification_message = f"تم إضافة طلب جديد للعميل {order.client.full_name} - {order.fabric_type} ({order.pieces_count} قطعة)"

        # Notify all users
        notify_all_users(
            title=notification_title,
            message=notification_message,
            notification_type='success',
            category='order',
            order_id=order.id
        )

        flash('تم إضافة الطلب بنجاح', 'success')
        return redirect(url_for('orders'))

    return render_template('orders/add.html', form=form)

@app.route('/api/calculate-price', methods=['POST'])
@login_required
def calculate_price_api():
    """API endpoint to calculate price based on fabric type, pieces, and rush order"""
    data = request.get_json()

    fabric_type = data.get('fabric_type')
    pieces_count = int(data.get('pieces_count', 1))
    is_rush_order = data.get('is_rush_order', False)

    if not fabric_type:
        return jsonify({'error': 'نوع القماش مطلوب'}), 400

    # Create temporary order object for calculation
    temp_order = Order(
        fabric_type=fabric_type,
        pieces_count=pieces_count,
        is_rush_order=is_rush_order
    )

    calculated_price = temp_order.calculate_price()
    breakdown = temp_order.get_pricing_breakdown()

    return jsonify({
        'total_price': calculated_price,
        'breakdown': breakdown
    })

@app.route('/orders/<int:id>')
@login_required
def view_order(id):
    order = Order.query.get_or_404(id)
    return render_template('orders/view.html', order=order)

@app.route('/orders/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_order(id):
    order = Order.query.get_or_404(id)
    form = OrderForm(obj=order)
    form.client_id.choices = [(c.id, c.full_name) for c in Client.query.all()]

    if form.validate_on_submit():
        form.populate_obj(order)
        order.updated_at = datetime.utcnow()
        db.session.commit()
        flash('تم تحديث الطلب بنجاح', 'success')
        return redirect(url_for('view_order', id=id))

    return render_template('orders/edit.html', form=form, order=order)

@app.route('/orders/<int:id>/status', methods=['GET', 'POST'])
@login_required
def update_order_status(id):
    order = Order.query.get_or_404(id)
    form = OrderStatusForm()

    if request.method == 'GET':
        # Pre-populate form with current values
        form.status.data = order.status
        if order.actual_delivery:
            form.actual_delivery.data = order.actual_delivery
        return render_template('orders/status.html', order=order, form=form)

    if form.validate_on_submit():
        old_status = order.status
        new_status = form.status.data

        # Check if status change is allowed
        if old_status != new_status and not order.can_change_to_status(new_status):
            flash(f'لا يمكن تغيير الحالة من "{order.get_status_display()}" إلى "{dict(form.status.choices)[new_status]}"', 'error')
            return render_template('orders/status.html', order=order, form=form)

        # Update order status
        order.status = new_status

        # Handle delivery date
        if new_status == 'delivered':
            if form.actual_delivery.data:
                order.actual_delivery = form.actual_delivery.data
            elif not order.actual_delivery:
                order.actual_delivery = date.today()
        elif new_status != 'delivered':
            # Clear delivery date if status changed from delivered
            order.actual_delivery = None

        # Add to status history
        if old_status != new_status:
            status_change = OrderStatusHistory(
                order_id=order.id,
                old_status=old_status,
                new_status=new_status,
                changed_by=current_user.id,
                change_reason=form.notes.data
            )
            db.session.add(status_change)

        # Update order notes if provided
        if form.notes.data:
            if order.notes:
                order.notes += f"\n\n[{datetime.now().strftime('%Y-%m-%d %H:%M')}] {form.notes.data}"
            else:
                order.notes = f"[{datetime.now().strftime('%Y-%m-%d %H:%M')}] {form.notes.data}"

        order.updated_at = datetime.utcnow()
        db.session.commit()

        # Success message with status change info
        if old_status != new_status:
            flash(f'تم تغيير حالة الطلب من "{dict(form.status.choices)[old_status]}" إلى "{dict(form.status.choices)[new_status]}"', 'success')

            # Create notification for status change
            status_messages = {
                'new': 'تم إنشاء طلب جديد',
                'ongoing': 'بدء العمل على الطلب',
                'ready': 'الطلب جاهز للاستلام',
                'delivered': 'تم تسليم الطلب'
            }

            notification_title = f"تحديث حالة الطلب #{order.id}"
            notification_message = f"تم تغيير حالة الطلب للعميل {order.client.full_name} إلى: {status_messages.get(new_status, new_status)}"

            # Notify all users
            notify_all_users(
                title=notification_title,
                message=notification_message,
                notification_type='info',
                category='order',
                order_id=order.id
            )
        else:
            flash('تم تحديث بيانات الطلب بنجاح', 'success')

        # TODO: Send notification to client if notify_client is checked
        if form.notify_client.data:
            # Here you can add SMS/Email notification logic
            pass

    return redirect(url_for('view_order', id=id))

@app.route('/orders/<int:id>/delete', methods=['POST'])
@login_required
def delete_order(id):
    """Delete an order"""
    order = Order.query.get_or_404(id)

    # Store order info before deletion
    order_info = f"#{order.id} - {order.client.full_name}"
    client_id = order.client_id

    db.session.delete(order)
    db.session.commit()

    # Create notification for order deletion
    notification_title = "حذف طلب"
    notification_message = f"تم حذف الطلب {order_info} بواسطة {current_user.full_name}"

    # Notify all users except current user
    users = User.query.filter(User.is_active == True, User.id != current_user.id).all()
    for user in users:
        create_notification(
            user_id=user.id,
            title=notification_title,
            message=notification_message,
            notification_type='warning',
            category='order',
            client_id=client_id
        )

    flash('تم حذف الطلب بنجاح', 'success')
    return redirect(url_for('orders'))

@app.route('/orders/<int:id>/invoice')
@login_required
def generate_invoice(id):
    """Generate and download Arabic invoice PDF"""
    order = Order.query.get_or_404(id)

    # Check if order is ready for invoice
    if order.status not in ['ready', 'delivered']:
        flash('لا يمكن إنشاء فاتورة للطلب في هذه الحالة', 'error')
        return redirect(url_for('view_order', id=id))

    try:
        # Create temporary file for PDF
        import tempfile
        temp_dir = tempfile.gettempdir()
        filename = f"invoice_{order.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        output_path = os.path.join(temp_dir, filename)

        # Generate invoice using simple generator (fallback for Arabic font issues)
        try:
            invoice_generator = ArabicInvoiceGenerator()
            invoice_generator.generate_invoice(order, output_path)
        except Exception as e:
            print(f"Arabic invoice generation failed: {e}")
            # Fallback to simple invoice generator
            invoice_generator = SimpleArabicInvoiceGenerator()
            invoice_generator.generate_invoice(order, output_path)

        # Send file to user
        return send_file(
            output_path,
            as_attachment=True,
            download_name=f"فاتورة_طلب_{order.id}.pdf",
            mimetype='application/pdf'
        )

    except Exception as e:
        flash(f'حدث خطأ في إنشاء الفاتورة: {str(e)}', 'error')
        return redirect(url_for('view_order', id=id))

@app.route('/reports')
@login_required
def reports():
    from utils.reports import get_dashboard_stats, generate_daily_report, generate_monthly_report

    # Get current date
    today = date.today()

    # Generate reports
    daily_report = generate_daily_report(today)
    monthly_report = generate_monthly_report(today.year, today.month)

    return render_template('reports/dashboard.html',
                         daily_report=daily_report,
                         monthly_report=monthly_report)

@app.route('/users')
@login_required
def users():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    users = User.query.all()
    return render_template('users/list.html', users=users)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    form = UserForm()
    if form.validate_on_submit():
        # Check if username already exists
        existing_user = User.query.filter_by(username=form.username.data).first()
        if existing_user:
            flash('اسم المستخدم موجود بالفعل', 'error')
            return render_template('users/add.html', form=form)

        user = User(
            username=form.username.data,
            email=form.email.data,
            full_name=form.full_name.data,
            role=form.role.data,
            is_active=form.is_active.data
        )

        if form.password.data:
            user.set_password(form.password.data)
        else:
            user.set_password('123456')  # Default password

        db.session.add(user)
        db.session.commit()
        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users'))

    return render_template('users/add.html', form=form)

@app.route('/users/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(id):
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(id)
    form = UserForm(obj=user)

    if form.validate_on_submit():
        # Check if username already exists (excluding current user)
        existing_user = User.query.filter(User.username == form.username.data, User.id != id).first()
        if existing_user:
            flash('اسم المستخدم موجود بالفعل', 'error')
            return render_template('users/edit.html', form=form, user=user)

        form.populate_obj(user)

        if form.password.data:
            user.set_password(form.password.data)


        db.session.commit()
        flash('تم تحديث بيانات المستخدم بنجاح', 'success')
        return redirect(url_for('users'))

    return render_template('users/edit.html', form=form, user=user)

@app.route('/users/<int:id>/delete', methods=['POST'])
@login_required
def delete_user(id):
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(id)

    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('users'))

    db.session.delete(user)
    db.session.commit()
    flash('تم حذف المستخدم بنجاح', 'success')
    return redirect(url_for('users'))

@app.route('/settings')
@login_required
def settings():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    # Get pricing rules
    pricing_rules = PricingRule.query.filter_by(is_active=True).all()

    # Get system settings
    settings_dict = {}
    system_settings = SystemSettings.query.all()
    for setting in system_settings:
        settings_dict[setting.setting_key] = setting.get_value()

    return render_template('settings/index.html',
                         pricing_rules=pricing_rules,
                         settings=settings_dict)

@app.route('/settings/pricing')
@login_required
def pricing_settings():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    pricing_rules = PricingRule.query.all()
    return render_template('settings/pricing.html', pricing_rules=pricing_rules)

@app.route('/settings/pricing/add', methods=['GET', 'POST'])
@login_required
def add_pricing_rule():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    form = PricingRuleForm()
    if form.validate_on_submit():
        # Check if fabric type already exists
        existing_rule = PricingRule.query.filter_by(fabric_type=form.fabric_type.data).first()
        if existing_rule:
            flash('قاعدة تسعير لهذا النوع من القماش موجودة بالفعل', 'error')
            return render_template('settings/pricing_form.html', form=form, title='إضافة قاعدة تسعير')

        pricing_rule = PricingRule(
            fabric_type=form.fabric_type.data,
            base_price=float(form.base_price.data),
            size_multiplier=float(form.size_multiplier.data),
            rush_multiplier=float(form.rush_multiplier.data),
            is_active=form.is_active.data
        )

        db.session.add(pricing_rule)
        db.session.commit()
        flash('تم إضافة قاعدة التسعير بنجاح', 'success')
        return redirect(url_for('pricing_settings'))

    return render_template('settings/pricing_form.html', form=form, title='إضافة قاعدة تسعير')

@app.route('/settings/pricing/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_pricing_rule(id):
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    pricing_rule = PricingRule.query.get_or_404(id)
    form = PricingRuleForm(obj=pricing_rule)

    if form.validate_on_submit():
        # Check if fabric type already exists (excluding current rule)
        existing_rule = PricingRule.query.filter(
            PricingRule.fabric_type == form.fabric_type.data,
            PricingRule.id != id
        ).first()
        if existing_rule:
            flash('قاعدة تسعير لهذا النوع من القماش موجودة بالفعل', 'error')
            return render_template('settings/pricing_form.html', form=form, pricing_rule=pricing_rule, title='تعديل قاعدة التسعير')

        form.populate_obj(pricing_rule)
        pricing_rule.base_price = float(form.base_price.data)
        pricing_rule.size_multiplier = float(form.size_multiplier.data)
        pricing_rule.rush_multiplier = float(form.rush_multiplier.data)

        db.session.commit()
        flash('تم تحديث قاعدة التسعير بنجاح', 'success')
        return redirect(url_for('pricing_settings'))

    return render_template('settings/pricing_form.html', form=form, pricing_rule=pricing_rule, title='تعديل قاعدة التسعير')

@app.route('/settings/pricing/<int:id>/delete', methods=['POST'])
@login_required
def delete_pricing_rule(id):
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    pricing_rule = PricingRule.query.get_or_404(id)
    db.session.delete(pricing_rule)
    db.session.commit()
    flash('تم حذف قاعدة التسعير بنجاح', 'success')
    return redirect(url_for('pricing_settings'))

@app.route('/settings/system', methods=['GET', 'POST'])
@login_required
def system_settings():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    form = SystemSettingsForm()

    if request.method == 'GET':
        # Load current settings
        settings_dict = {}
        system_settings = SystemSettings.query.all()
        for setting in system_settings:
            settings_dict[setting.setting_key] = setting.get_value()

        # Populate form with current values
        for field_name in form._fields:
            if field_name in settings_dict:
                getattr(form, field_name).data = settings_dict[field_name]

    if request.method == 'POST':
        # Check if this is an AJAX request for partial save
        section = request.form.get('section')

        if section:
            # Handle partial save
            try:
                if section == 'company':
                    fields = ['company_name', 'company_address', 'company_phone', 'company_email']
                elif section == 'pricing':
                    fields = ['default_delivery_days', 'rush_order_days', 'minimum_order_amount']
                elif section == 'discount':
                    fields = ['bulk_discount_threshold', 'bulk_discount_percentage', 'vip_discount_percentage']
                elif section == 'tax':
                    fields = ['tax_enabled', 'tax_percentage', 'tax_number']
                elif section == 'notifications':
                    fields = ['sms_notifications', 'email_notifications', 'whatsapp_notifications']
                elif section == 'system':
                    fields = ['auto_backup', 'backup_frequency']
                else:
                    return jsonify({'success': False, 'message': 'قسم غير صحيح'})

                # Update only the specified fields
                for field_name in fields:
                    if field_name in request.form:
                        value = request.form.get(field_name)

                        # Handle boolean fields
                        if field_name in ['tax_enabled', 'sms_notifications', 'email_notifications', 'whatsapp_notifications', 'auto_backup']:
                            value = value.lower() in ['true', '1', 'on']

                        setting = SystemSettings.query.filter_by(setting_key=field_name).first()
                        if not setting:
                            setting_type = 'boolean' if isinstance(value, bool) else 'number' if field_name in ['default_delivery_days', 'rush_order_days', 'minimum_order_amount', 'bulk_discount_threshold', 'bulk_discount_percentage', 'vip_discount_percentage', 'tax_percentage'] else 'text'
                            setting = SystemSettings(
                                setting_key=field_name,
                                setting_type=setting_type,
                                category=section
                            )
                            db.session.add(setting)

                        setting.set_value(value)

                db.session.commit()
                return jsonify({'success': True, 'message': f'تم حفظ {section} بنجاح'})

            except Exception as e:
                db.session.rollback()
                return jsonify({'success': False, 'message': str(e)})

        # Handle full form submission
        if form.validate_on_submit():
            # Update or create settings
            for field_name, field in form._fields.items():
                if field_name != 'csrf_token':
                    setting = SystemSettings.query.filter_by(setting_key=field_name).first()
                    if not setting:
                        setting = SystemSettings(
                            setting_key=field_name,
                            setting_type='boolean' if isinstance(field.data, bool) else 'number' if isinstance(field.data, (int, float)) else 'text',
                            category='system'
                        )
                        db.session.add(setting)

                    setting.set_value(field.data)

            db.session.commit()
            flash('تم حفظ جميع إعدادات النظام بنجاح', 'success')
            return redirect(url_for('system_settings'))

    return render_template('settings/system.html', form=form)



@app.route('/orders/<int:id>/invoice/view')
@login_required
def view_invoice_html(id):
    """View invoice in browser"""
    order = Order.query.get_or_404(id)

    # Check if order is ready for invoice
    if order.status not in ['ready', 'delivered']:
        flash('لا يمكن عرض فاتورة للطلب في هذه الحالة', 'error')
        return redirect(url_for('view_order', id=id))

    return render_template('orders/invoice.html', order=order, now=datetime.now())

@app.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile management"""
    form = ProfileForm(obj=current_user)

    if form.validate_on_submit():
        # Update basic info
        current_user.full_name = form.full_name.data
        current_user.email = form.email.data
        current_user.phone = form.phone.data

        # Update notification preferences
        current_user.email_notifications = form.email_notifications.data
        current_user.sms_notifications = form.sms_notifications.data
        current_user.desktop_notifications = form.desktop_notifications.data

        # Update password if provided
        if form.new_password.data:
            if form.current_password.data and current_user.check_password(form.current_password.data):
                current_user.set_password(form.new_password.data)
                flash('تم تحديث كلمة المرور بنجاح', 'success')
            else:
                flash('كلمة المرور الحالية غير صحيحة', 'error')
                return render_template('profile.html', form=form)

        db.session.commit()
        flash('تم تحديث الملف الشخصي بنجاح', 'success')
        return redirect(url_for('profile'))

    return render_template('profile.html', form=form)

@app.route('/notifications')
@login_required
def notifications():
    """View user notifications"""
    page = request.args.get('page', 1, type=int)
    unread_only = request.args.get('unread', False, type=bool)

    query = Notification.query.filter_by(user_id=current_user.id)

    if unread_only:
        query = query.filter_by(is_read=False)

    notifications = query.order_by(Notification.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False)

    # Count unread notifications
    unread_count = Notification.query.filter_by(
        user_id=current_user.id, is_read=False).count()

    return render_template('notifications/list.html',
                         notifications=notifications,
                         unread_count=unread_count,
                         unread_only=unread_only)

@app.route('/notifications/<int:id>/read', methods=['POST'])
@login_required
def mark_notification_read(id):
    """Mark notification as read"""
    notification = Notification.query.filter_by(
        id=id, user_id=current_user.id).first_or_404()

    notification.mark_as_read()

    if request.is_json:
        return jsonify({'success': True})

    return redirect(url_for('notifications'))

@app.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """Mark all notifications as read"""
    Notification.query.filter_by(
        user_id=current_user.id, is_read=False).update({'is_read': True})
    db.session.commit()

    if request.is_json:
        return jsonify({'success': True})

    flash('تم تمييز جميع الإشعارات كمقروءة', 'success')
    return redirect(url_for('notifications'))

@app.route('/api/notifications/unread-count')
@login_required
def get_unread_notifications_count():
    """Get count of unread notifications"""
    count = Notification.query.filter_by(
        user_id=current_user.id, is_read=False).count()
    return jsonify({'count': count})

@app.route('/api/notifications/recent')
@login_required
def get_recent_notifications():
    """Get recent notifications for dropdown"""
    notifications = Notification.query.filter_by(
        user_id=current_user.id).order_by(
        Notification.created_at.desc()).limit(5).all()

    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': notification.id,
            'title': notification.title,
            'message': notification.message[:100] + '...' if len(notification.message) > 100 else notification.message,
            'type': notification.type,
            'icon': notification.get_icon(),
            'color': notification.get_color(),
            'is_read': notification.is_read,
            'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M'),
            'order_id': notification.order_id,
            'client_id': notification.client_id
        })

    return jsonify({'notifications': notifications_data})

@app.route('/api/notifications/check-system', methods=['POST'])
@login_required
def check_system_notifications():
    """Manually trigger system notification checks"""
    if not current_user.is_admin():
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        # Check for overdue orders
        check_overdue_orders()

        # Check for upcoming deliveries
        check_upcoming_deliveries()

        # Check for orders ready for pickup (status = ready for more than 3 days)
        from datetime import date, timedelta
        three_days_ago = date.today() - timedelta(days=3)

        ready_orders = Order.query.filter(
            Order.status == 'ready',
            Order.updated_at <= datetime.combine(three_days_ago, datetime.min.time())
        ).all()

        for order in ready_orders:
            # Check if we already sent a notification for this order today
            existing_notification = Notification.query.filter(
                Notification.order_id == order.id,
                Notification.category == 'pickup_reminder',
                Notification.created_at >= datetime.combine(date.today(), datetime.min.time())
            ).first()

            if not existing_notification:
                title = f"تذكير: طلب جاهز للاستلام #{order.id}"
                message = f"الطلب للعميل {order.client.full_name} جاهز للاستلام منذ أكثر من 3 أيام. يرجى التواصل مع العميل."

                notify_all_users(
                    title=title,
                    message=message,
                    notification_type='info',
                    category='pickup_reminder',
                    order_id=order.id,
                    client_id=order.client_id
                )

        return jsonify({
            'success': True,
            'message': 'تم فحص النظام وإرسال الإشعارات المطلوبة'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

def create_notification(user_id, title, message, notification_type='info',
                       category='general', order_id=None, client_id=None):
    """Create a new notification"""
    notification = Notification(
        user_id=user_id,
        title=title,
        message=message,
        type=notification_type,
        category=category,
        order_id=order_id,
        client_id=client_id
    )
    db.session.add(notification)
    db.session.commit()
    return notification

def notify_all_users(title, message, notification_type='info', category='system', order_id=None, client_id=None):
    """Send notification to all active users"""
    users = User.query.filter_by(is_active=True).all()
    for user in users:
        create_notification(user.id, title, message, notification_type, category, order_id, client_id)

def check_overdue_orders():
    """Check for overdue orders and create notifications"""
    from datetime import date
    today = date.today()

    # Find orders that are overdue
    overdue_orders = Order.query.filter(
        Order.expected_delivery < today,
        Order.status.in_(['new', 'ongoing'])
    ).all()

    for order in overdue_orders:
        days_overdue = (today - order.expected_delivery).days

        # Check if we already sent a notification for this order today
        existing_notification = Notification.query.filter(
            Notification.order_id == order.id,
            Notification.category == 'overdue',
            Notification.created_at >= datetime.combine(today, datetime.min.time())
        ).first()

        if not existing_notification:
            title = f"طلب متأخر #{order.id}"
            message = f"الطلب للعميل {order.client.full_name} متأخر بـ {days_overdue} يوم. التسليم المتوقع كان {order.expected_delivery.strftime('%Y-%m-%d')}"

            notify_all_users(
                title=title,
                message=message,
                notification_type='warning',
                category='overdue',
                order_id=order.id,
                client_id=order.client_id
            )

def check_upcoming_deliveries():
    """Check for orders due tomorrow and create notifications"""
    from datetime import date, timedelta
    tomorrow = date.today() + timedelta(days=1)

    # Find orders due tomorrow
    upcoming_orders = Order.query.filter(
        Order.expected_delivery == tomorrow,
        Order.status.in_(['new', 'ongoing'])
    ).all()

    for order in upcoming_orders:
        # Check if we already sent a notification for this order today
        existing_notification = Notification.query.filter(
            Notification.order_id == order.id,
            Notification.category == 'reminder',
            Notification.created_at >= datetime.combine(date.today(), datetime.min.time())
        ).first()

        if not existing_notification:
            title = f"تذكير: طلب مستحق غداً #{order.id}"
            message = f"الطلب للعميل {order.client.full_name} مستحق التسليم غداً ({tomorrow.strftime('%Y-%m-%d')}). الحالة الحالية: {order.get_status_display()}"

            notify_all_users(
                title=title,
                message=message,
                notification_type='info',
                category='reminder',
                order_id=order.id,
                client_id=order.client_id
            )

def generate_daily_summary_notification():
    """Generate daily summary notification for admins"""
    from datetime import date, timedelta
    today = date.today()
    yesterday = today - timedelta(days=1)

    # Get statistics
    new_orders_today = Order.query.filter(
        Order.order_date >= today
    ).count()

    completed_orders_today = Order.query.filter(
        Order.status == 'delivered',
        Order.actual_delivery >= today
    ).count()

    overdue_orders = Order.query.filter(
        Order.expected_delivery < today,
        Order.status.in_(['new', 'ongoing'])
    ).count()

    ready_orders = Order.query.filter(
        Order.status == 'ready'
    ).count()

    # Create summary message
    title = f"ملخص يومي - {today.strftime('%Y-%m-%d')}"
    message = f"""
📊 ملخص أداء اليوم:
• طلبات جديدة: {new_orders_today}
• طلبات مكتملة: {completed_orders_today}
• طلبات متأخرة: {overdue_orders}
• طلبات جاهزة للاستلام: {ready_orders}

{f'⚠️ يوجد {overdue_orders} طلب متأخر يحتاج متابعة' if overdue_orders > 0 else '✅ لا توجد طلبات متأخرة'}
    """.strip()

    # Send to admins only
    admin_users = User.query.filter_by(role='admin', is_active=True).all()
    for admin in admin_users:
        create_notification(
            user_id=admin.id,
            title=title,
            message=message,
            notification_type='info' if overdue_orders == 0 else 'warning',
            category='daily_summary'
        )

@app.route('/api/notifications/daily-summary', methods=['POST'])
@login_required
def trigger_daily_summary():
    """Manually trigger daily summary notification"""
    if not current_user.is_admin():
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        generate_daily_summary_notification()
        return jsonify({
            'success': True,
            'message': 'تم إرسال الملخص اليومي'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@app.route('/notifications/admin')
@login_required
def notifications_admin_panel():
    """Admin panel for notifications management"""
    if not current_user.is_admin():
        flash('غير مصرح لك بالوصول لهذه الصفحة', 'error')
        return redirect(url_for('notifications'))

    # Get statistics
    total_notifications = Notification.query.count()
    unread_notifications = Notification.query.filter_by(is_read=False).count()

    from datetime import date
    today = date.today()
    today_notifications = Notification.query.filter(
        Notification.created_at >= datetime.combine(today, datetime.min.time())
    ).count()

    active_users = User.query.filter_by(is_active=True).count()

    # Get recent notifications (last 20)
    recent_notifications = Notification.query.order_by(
        Notification.created_at.desc()
    ).limit(20).all()

    return render_template('notifications/admin_panel.html',
                         total_notifications=total_notifications,
                         unread_notifications=unread_notifications,
                         today_notifications=today_notifications,
                         active_users=active_users,
                         recent_notifications=recent_notifications)

@app.route('/api/notifications/custom', methods=['POST'])
@login_required
def send_custom_notification():
    """Send custom notification to selected users"""
    if not current_user.is_admin():
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        data = request.get_json()
        title = data.get('title')
        message = data.get('message')
        notification_type = data.get('type', 'info')
        recipients = data.get('recipients', 'all')

        if not title or not message:
            return jsonify({
                'success': False,
                'message': 'العنوان والرسالة مطلوبان'
            }), 400

        # Determine target users
        if recipients == 'all':
            target_users = User.query.filter_by(is_active=True).all()
        elif recipients == 'admins':
            target_users = User.query.filter_by(role='admin', is_active=True).all()
        elif recipients == 'operators':
            target_users = User.query.filter_by(role='operator', is_active=True).all()
        else:
            target_users = User.query.filter_by(is_active=True).all()

        # Send notifications
        for user in target_users:
            create_notification(
                user_id=user.id,
                title=title,
                message=message,
                notification_type=notification_type,
                category='custom'
            )

        return jsonify({
            'success': True,
            'message': f'تم إرسال الإشعار إلى {len(target_users)} مستخدم'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

# Data Management API Routes
@app.route('/api/backup/create', methods=['POST'])
@login_required
def create_backup():
    """Create a backup of all data"""
    if not current_user.is_admin():
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        import json
        import zipfile
        from datetime import datetime

        # Create backup directory if not exists
        backup_dir = os.path.join(app.root_path, 'backups')
        os.makedirs(backup_dir, exist_ok=True)

        # Create backup filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_{timestamp}.zip'
        backup_path = os.path.join(backup_dir, backup_filename)

        # Create backup data
        backup_data = {
            'timestamp': timestamp,
            'created_by': current_user.username,
            'clients': [],
            'orders': [],
            'measurements': [],
            'users': [],
            'notifications': [],
            'pricing_rules': [],
            'system_settings': []
        }

        # Export clients
        for client in Client.query.all():
            backup_data['clients'].append({
                'id': client.id,
                'full_name': client.full_name,
                'mobile_number': client.mobile_number,
                'address': client.address,
                'created_at': client.created_at.isoformat() if client.created_at else None
            })

        # Export orders
        for order in Order.query.all():
            backup_data['orders'].append({
                'id': order.id,
                'client_id': order.client_id,
                'fabric_type': order.fabric_type,
                'pieces_count': order.pieces_count,
                'total_price': float(order.total_price),
                'status': order.status,
                'order_date': order.order_date.isoformat() if order.order_date else None,
                'expected_delivery': order.expected_delivery.isoformat() if order.expected_delivery else None,
                'actual_delivery': order.actual_delivery.isoformat() if order.actual_delivery else None,
                'special_requests': order.special_requests,
                'notes': order.notes,
                'is_rush_order': order.is_rush_order,
                'fabric_photo_path': order.fabric_photo_path,
                'created_at': order.created_at.isoformat() if order.created_at else None
            })

        # Export measurements
        for measurement in Measurement.query.all():
            backup_data['measurements'].append({
                'id': measurement.id,
                'client_id': measurement.client_id,
                'chest': float(measurement.chest) if measurement.chest else None,
                'waist': float(measurement.waist) if measurement.waist else None,
                'shoulder': float(measurement.shoulder) if measurement.shoulder else None,
                'sleeve': float(measurement.sleeve) if measurement.sleeve else None,
                'length': float(measurement.length) if measurement.length else None,
                'neck': float(measurement.neck) if measurement.neck else None,
                'notes': measurement.notes,
                'created_at': measurement.created_at.isoformat() if measurement.created_at else None
            })

        # Export users (without passwords)
        for user in User.query.all():
            backup_data['users'].append({
                'id': user.id,
                'username': user.username,
                'full_name': user.full_name,
                'role': user.role,
                'is_active': user.is_active,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'created_at': user.created_at.isoformat() if user.created_at else None
            })

        # Export notifications (last 1000)
        for notification in Notification.query.order_by(Notification.created_at.desc()).limit(1000).all():
            backup_data['notifications'].append({
                'id': notification.id,
                'user_id': notification.user_id,
                'title': notification.title,
                'message': notification.message,
                'notification_type': notification.notification_type,
                'category': notification.category,
                'is_read': notification.is_read,
                'created_at': notification.created_at.isoformat() if notification.created_at else None
            })

        # Export pricing rules
        for rule in PricingRule.query.all():
            backup_data['pricing_rules'].append({
                'id': rule.id,
                'fabric_type': rule.fabric_type,
                'base_price': float(rule.base_price),
                'rush_multiplier': float(rule.rush_multiplier),
                'is_active': rule.is_active,
                'created_at': rule.created_at.isoformat() if rule.created_at else None
            })

        # Export system settings
        for setting in SystemSettings.query.all():
            backup_data['system_settings'].append({
                'id': setting.id,
                'key': setting.key,
                'value': setting.value,
                'description': setting.description,
                'updated_at': setting.updated_at.isoformat() if setting.updated_at else None
            })

        # Create ZIP file with backup data
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add JSON data
            json_data = json.dumps(backup_data, ensure_ascii=False, indent=2)
            zipf.writestr(f'data_{timestamp}.json', json_data)

            # Add uploaded files if they exist
            upload_folder = app.config.get('UPLOAD_FOLDER', 'static/uploads')
            if os.path.exists(upload_folder):
                for root, dirs, files in os.walk(upload_folder):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, upload_folder)
                        zipf.write(file_path, f'uploads/{arcname}')

        # Store backup info in session or database for later download
        app.config['LAST_BACKUP_PATH'] = backup_path

        return jsonify({
            'success': True,
            'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
            'filename': backup_filename,
            'size': os.path.getsize(backup_path)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}'
        }), 500

@app.route('/api/backup/download')
@login_required
def download_backup():
    """Download the latest backup file"""
    if not current_user.is_admin():
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        backup_path = app.config.get('LAST_BACKUP_PATH')
        if backup_path and os.path.exists(backup_path):
            return send_file(backup_path, as_attachment=True)
        else:
            # Find the latest backup file
            backup_dir = os.path.join(app.root_path, 'backups')
            if os.path.exists(backup_dir):
                backup_files = [f for f in os.listdir(backup_dir) if f.startswith('backup_') and f.endswith('.zip')]
                if backup_files:
                    latest_backup = max(backup_files)
                    backup_path = os.path.join(backup_dir, latest_backup)
                    return send_file(backup_path, as_attachment=True)

            flash('لا توجد نسخة احتياطية متاحة', 'error')
            return redirect(url_for('system_settings'))

    except Exception as e:
        flash(f'حدث خطأ في تحميل النسخة الاحتياطية: {str(e)}', 'error')
        return redirect(url_for('system_settings'))

@app.route('/api/data/delete-old-notifications', methods=['POST'])
@login_required
def delete_old_notifications():
    """Delete notifications older than 30 days"""
    if not current_user.is_admin():
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        from datetime import datetime, timedelta

        # Calculate date 30 days ago
        thirty_days_ago = datetime.now() - timedelta(days=30)

        # Delete old notifications
        old_notifications = Notification.query.filter(
            Notification.created_at < thirty_days_ago
        ).all()

        deleted_count = len(old_notifications)

        for notification in old_notifications:
            db.session.delete(notification)

        db.session.commit()

        # Create notification about the cleanup
        create_notification(
            user_id=current_user.id,
            title="تنظيف الإشعارات",
            message=f"تم حذف {deleted_count} إشعار قديم (أقدم من 30 يوماً)",
            notification_type='info',
            category='system'
        )

        return jsonify({
            'success': True,
            'message': f'تم حذف {deleted_count} إشعار قديم',
            'deleted_count': deleted_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في حذف الإشعارات القديمة: {str(e)}'
        }), 500

@app.route('/api/data/delete-all-orders', methods=['POST'])
@login_required
def delete_all_orders():
    """Delete all orders and related data"""
    if not current_user.is_admin():
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        # Count orders before deletion
        orders_count = Order.query.count()

        # Delete all order status history first (foreign key constraint)
        OrderStatusHistory.query.delete()

        # Delete all orders
        Order.query.delete()

        db.session.commit()

        # Create notification about the deletion
        create_notification(
            user_id=current_user.id,
            title="حذف جميع الطلبات",
            message=f"تم حذف جميع الطلبات ({orders_count} طلب) بواسطة {current_user.full_name}",
            notification_type='warning',
            category='system'
        )

        return jsonify({
            'success': True,
            'message': f'تم حذف جميع الطلبات ({orders_count} طلب)',
            'deleted_count': orders_count
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في حذف الطلبات: {str(e)}'
        }), 500

@app.route('/api/data/delete-all-clients', methods=['POST'])
@login_required
def delete_all_clients():
    """Delete all clients and related data"""
    if not current_user.is_admin():
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        # Count clients before deletion
        clients_count = Client.query.count()
        orders_count = Order.query.count()
        measurements_count = Measurement.query.count()

        # Delete in correct order due to foreign key constraints
        OrderStatusHistory.query.delete()
        Order.query.delete()
        Measurement.query.delete()
        Client.query.delete()

        db.session.commit()

        # Create notification about the deletion
        create_notification(
            user_id=current_user.id,
            title="حذف جميع العملاء",
            message=f"تم حذف جميع العملاء ({clients_count} عميل) والطلبات ({orders_count} طلب) والمقاسات ({measurements_count} مقاس) بواسطة {current_user.full_name}",
            notification_type='warning',
            category='system'
        )

        return jsonify({
            'success': True,
            'message': f'تم حذف جميع العملاء ({clients_count} عميل) والبيانات المرتبطة',
            'deleted_count': clients_count + orders_count + measurements_count
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في حذف العملاء: {str(e)}'
        }), 500

@app.route('/api/data/delete-all-data', methods=['POST'])
@login_required
def delete_all_data():
    """Delete all data except users and system settings"""
    if not current_user.is_admin():
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        # Count data before deletion
        clients_count = Client.query.count()
        orders_count = Order.query.count()
        measurements_count = Measurement.query.count()
        notifications_count = Notification.query.count()
        pricing_rules_count = PricingRule.query.count()

        # Delete in correct order due to foreign key constraints
        OrderStatusHistory.query.delete()
        Order.query.delete()
        Measurement.query.delete()
        Client.query.delete()
        Notification.query.delete()
        PricingRule.query.delete()

        db.session.commit()

        # Create notification about the deletion
        create_notification(
            user_id=current_user.id,
            title="حذف جميع البيانات",
            message=f"تم حذف جميع البيانات بواسطة {current_user.full_name}: {clients_count} عميل، {orders_count} طلب، {measurements_count} مقاس، {notifications_count} إشعار، {pricing_rules_count} قاعدة تسعير",
            notification_type='error',
            category='system'
        )

        total_deleted = clients_count + orders_count + measurements_count + notifications_count + pricing_rules_count

        return jsonify({
            'success': True,
            'message': f'تم حذف جميع البيانات ({total_deleted} عنصر)',
            'deleted_count': total_deleted
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في حذف البيانات: {str(e)}'
        }), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Create default admin user if not exists
        admin = User.query.filter_by(username=app.config['DEFAULT_ADMIN_USERNAME']).first()
        if not admin:
            admin = User(
                username=app.config['DEFAULT_ADMIN_USERNAME'],
                full_name='مدير النظام',
                role='admin'
            )
            admin.set_password(app.config['DEFAULT_ADMIN_PASSWORD'])
            db.session.add(admin)
            db.session.commit()
            print(f"Default admin created: {app.config['DEFAULT_ADMIN_USERNAME']}/{app.config['DEFAULT_ADMIN_PASSWORD']}")

            # Create sample notifications for demo
            sample_notifications = [
                {
                    'title': 'مرحباً بك في النظام',
                    'message': 'تم إنشاء حسابك بنجاح. يمكنك الآن البدء في استخدام نظام إدارة خياطة الرجال القطري.',
                    'type': 'success',
                    'category': 'system'
                },
                {
                    'title': 'تذكير: طلبات متأخرة',
                    'message': 'يوجد طلبات تجاوزت موعد التسليم المتوقع. يرجى مراجعة قائمة الطلبات.',
                    'type': 'warning',
                    'category': 'order'
                },
                {
                    'title': 'نصيحة: استخدام الفواتير',
                    'message': 'يمكنك الآن إنشاء فواتير عربية احترافية للطلبات الجاهزة والمسلمة.',
                    'type': 'info',
                    'category': 'system'
                }
            ]

            for notif_data in sample_notifications:
                create_notification(
                    user_id=admin.id,
                    title=notif_data['title'],
                    message=notif_data['message'],
                    notification_type=notif_data['type'],
                    category=notif_data['category']
                )

    app.run(debug=True, host='0.0.0.0', port=5050)
