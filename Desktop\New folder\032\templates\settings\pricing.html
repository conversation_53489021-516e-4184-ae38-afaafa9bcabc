{% extends "base.html" %}

{% block title %}إعدادات الأسعار - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tags me-2"></i>إعدادات الأسعار</h2>
    <div>
        <a href="{{ url_for('add_pricing_rule') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إضافة قاعدة تسعير
        </a>
        <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للإعدادات
        </a>
    </div>
</div>

<!-- Pricing Rules -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>قواعد التسعير</h5>
            </div>
            <div class="card-body">
                {% if pricing_rules %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>نوع القماش</th>
                                <th>السعر الأساسي</th>
                                <th>مضاعف الحجم</th>
                                <th>مضاعف العاجل</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rule in pricing_rules %}
                            <tr>
                                <td>
                                    <strong>{{ rule.fabric_type }}</strong>
                                </td>
                                <td>
                                    <span class="text-success">{{ "%.2f"|format(rule.base_price) }} ريال</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ "%.1f"|format(rule.size_multiplier) }}x</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">{{ "%.1f"|format(rule.rush_multiplier) }}x</span>
                                </td>
                                <td>
                                    {% if rule.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">معطل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ rule.created_at.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('edit_pricing_rule', id=rule.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('delete_pricing_rule', id=rule.id) }}" 
                                              style="display: inline;" 
                                              onsubmit="return confirm('هل أنت متأكد من حذف هذه القاعدة؟')">
                                            <button type="submit" class="btn btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد قواعد تسعير</h5>
                    <p class="text-muted">ابدأ بإضافة قواعد التسعير لأنواع الأقمشة المختلفة</p>
                    <a href="{{ url_for('add_pricing_rule') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة أول قاعدة تسعير
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Pricing Calculator -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>حاسبة الأسعار</h6>
            </div>
            <div class="card-body">
                <form id="pricing-calculator">
                    <div class="mb-3">
                        <label class="form-label">نوع القماش</label>
                        <select class="form-select" id="fabric-type">
                            <option value="">اختر نوع القماش</option>
                            {% for rule in pricing_rules %}
                            {% if rule.is_active %}
                            <option value="{{ rule.id }}" 
                                    data-base-price="{{ rule.base_price }}"
                                    data-size-multiplier="{{ rule.size_multiplier }}"
                                    data-rush-multiplier="{{ rule.rush_multiplier }}">
                                {{ rule.fabric_type }}
                            </option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">عدد القطع</label>
                        <input type="number" class="form-control" id="pieces-count" value="1" min="1">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is-rush">
                            <label class="form-check-label" for="is-rush">
                                طلب عاجل
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is-bulk">
                            <label class="form-check-label" for="is-bulk">
                                خصم جماعي (5+ قطع)
                            </label>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="pricing-breakdown">
                        <div class="d-flex justify-content-between mb-2">
                            <span>السعر الأساسي:</span>
                            <span id="base-total">0.00 ريال</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2" id="rush-fee" style="display: none;">
                            <span>رسوم العاجل:</span>
                            <span id="rush-amount">0.00 ريال</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2" id="bulk-discount" style="display: none;">
                            <span>خصم جماعي:</span>
                            <span id="discount-amount" class="text-success">-0.00 ريال</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span id="subtotal">0.00 ريال</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الضريبة (15%):</span>
                            <span id="tax-amount">0.00 ريال</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>المجموع النهائي:</strong>
                            <strong id="final-total" class="text-success">0.00 ريال</strong>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات التسعير</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>كيفية عمل التسعير:</h6>
                    <ul class="mb-0">
                        <li><strong>السعر الأساسي:</strong> سعر القطعة الواحدة</li>
                        <li><strong>مضاعف الحجم:</strong> يطبق حسب حجم العميل</li>
                        <li><strong>مضاعف العاجل:</strong> يطبق على الطلبات العاجلة</li>
                        <li><strong>خصم جماعي:</strong> 10% للطلبات 5+ قطع</li>
                        <li><strong>الضريبة:</strong> 15% ضريبة القيمة المضافة</li>
                    </ul>
                </div>
                
                <div class="mt-3">
                    <h6>أنواع الأقمشة الشائعة:</h6>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">
                                • ثوب صيفي<br>
                                • ثوب شتوي<br>
                                • بشت عادي<br>
                                • بشت فاخر
                            </small>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">
                                • دشداشة<br>
                                • جلابية<br>
                                • قميص تراثي<br>
                                • عباءة رجالية
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>نصائح التسعير:</h6>
                    <small class="text-muted">
                        • راجع الأسعار دورياً حسب تكلفة المواد<br>
                        • اعتبر وقت العمل في التسعير<br>
                        • قدم خصومات للعملاء المميزين<br>
                        • احسب تكلفة الخدمات الإضافية
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fabricSelect = document.getElementById('fabric-type');
    const piecesInput = document.getElementById('pieces-count');
    const rushCheckbox = document.getElementById('is-rush');
    const bulkCheckbox = document.getElementById('is-bulk');
    
    function calculatePrice() {
        const selectedOption = fabricSelect.selectedOptions[0];
        if (!selectedOption || !selectedOption.value) {
            resetCalculator();
            return;
        }
        
        const basePrice = parseFloat(selectedOption.dataset.basePrice);
        const sizeMultiplier = parseFloat(selectedOption.dataset.sizeMultiplier);
        const rushMultiplier = parseFloat(selectedOption.dataset.rushMultiplier);
        const pieces = parseInt(piecesInput.value) || 1;
        const isRush = rushCheckbox.checked;
        const isBulk = bulkCheckbox.checked;
        
        // Calculate base total
        let baseTotal = basePrice * pieces * sizeMultiplier;
        document.getElementById('base-total').textContent = baseTotal.toFixed(2) + ' ريال';
        
        let subtotal = baseTotal;
        
        // Apply rush fee
        if (isRush) {
            const rushFee = baseTotal * (rushMultiplier - 1);
            document.getElementById('rush-amount').textContent = rushFee.toFixed(2) + ' ريال';
            document.getElementById('rush-fee').style.display = 'flex';
            subtotal += rushFee;
        } else {
            document.getElementById('rush-fee').style.display = 'none';
        }
        
        // Apply bulk discount
        if (isBulk && pieces >= 5) {
            const discount = subtotal * 0.1;
            document.getElementById('discount-amount').textContent = '-' + discount.toFixed(2) + ' ريال';
            document.getElementById('bulk-discount').style.display = 'flex';
            subtotal -= discount;
        } else {
            document.getElementById('bulk-discount').style.display = 'none';
        }
        
        document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ريال';
        
        // Calculate tax
        const tax = subtotal * 0.15;
        document.getElementById('tax-amount').textContent = tax.toFixed(2) + ' ريال';
        
        // Final total
        const finalTotal = subtotal + tax;
        document.getElementById('final-total').textContent = finalTotal.toFixed(2) + ' ريال';
    }
    
    function resetCalculator() {
        document.getElementById('base-total').textContent = '0.00 ريال';
        document.getElementById('subtotal').textContent = '0.00 ريال';
        document.getElementById('tax-amount').textContent = '0.00 ريال';
        document.getElementById('final-total').textContent = '0.00 ريال';
        document.getElementById('rush-fee').style.display = 'none';
        document.getElementById('bulk-discount').style.display = 'none';
    }
    
    // Auto-check bulk discount for 5+ pieces
    piecesInput.addEventListener('input', function() {
        if (parseInt(this.value) >= 5) {
            bulkCheckbox.checked = true;
        } else {
            bulkCheckbox.checked = false;
        }
        calculatePrice();
    });
    
    // Event listeners
    fabricSelect.addEventListener('change', calculatePrice);
    rushCheckbox.addEventListener('change', calculatePrice);
    bulkCheckbox.addEventListener('change', calculatePrice);
    
    // Initial calculation
    calculatePrice();
});
</script>
{% endblock %}
