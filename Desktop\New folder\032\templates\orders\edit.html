{% extends "base.html" %}

{% block title %}تعديل طلب #{{ order.id }} - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-edit me-2"></i>تعديل طلب #{{ order.id }}</h2>
    <div>
        <a href="{{ url_for('view_order', id=order.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للطلب
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>تعديل تفاصيل الطلب</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.client_id.label(class="form-label") }}
                            {{ form.client_id(class="form-select" + (" is-invalid" if form.client_id.errors else "")) }}
                            {% if form.client_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.client_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.fabric_type.label(class="form-label") }}
                            {{ form.fabric_type(class="form-select" + (" is-invalid" if form.fabric_type.errors else "")) }}
                            {% if form.fabric_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.fabric_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            {{ form.pieces_count.label(class="form-label") }}
                            {{ form.pieces_count(class="form-control" + (" is-invalid" if form.pieces_count.errors else "")) }}
                            {% if form.pieces_count.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.pieces_count.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.total_price.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.total_price(class="form-control" + (" is-invalid" if form.total_price.errors else ""), step="0.01") }}
                                <span class="input-group-text">ريال</span>
                            </div>
                            {% if form.total_price.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.total_price.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.expected_delivery.label(class="form-label") }}
                            {{ form.expected_delivery(class="form-control" + (" is-invalid" if form.expected_delivery.errors else "")) }}
                            {% if form.expected_delivery.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.expected_delivery.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.fabric_photo.label(class="form-label") }}
                        {{ form.fabric_photo(class="form-control" + (" is-invalid" if form.fabric_photo.errors else "")) }}
                        {% if form.fabric_photo.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.fabric_photo.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% if order.fabric_photo_path %}
                        <div class="mt-2">
                            <small class="text-muted">الصورة الحالية:</small><br>
                            <img src="{{ url_for('static', filename='uploads/' + order.fabric_photo_path) }}" 
                                 class="img-thumbnail" style="max-width: 150px;">
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.special_requests.label(class="form-label") }}
                        {{ form.special_requests(class="form-control" + (" is-invalid" if form.special_requests.errors else ""), rows="3") }}
                        {% if form.special_requests.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.special_requests.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="3") }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('view_order', id=order.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Order Summary -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>ملخص الطلب</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>رقم الطلب:</strong><br>
                    <span class="text-muted">#{{ order.id }}</span>
                </div>
                <div class="mb-3">
                    <strong>تاريخ الإنشاء:</strong><br>
                    <span class="text-muted">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
                <div class="mb-3">
                    <strong>الحالة الحالية:</strong><br>
                    <span class="badge status-{{ order.status }}">
                        {{ order.get_status_display() }}
                    </span>
                </div>
                {% if order.actual_delivery %}
                <div class="mb-3">
                    <strong>تاريخ التسليم الفعلي:</strong><br>
                    <span class="text-success">{{ order.actual_delivery.strftime('%Y-%m-%d') }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Client Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل</h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>الاسم:</strong><br>
                    <small>{{ order.client.full_name }}</small>
                </div>
                <div class="mb-2">
                    <strong>الجوال:</strong><br>
                    <small>{{ order.client.mobile_number }}</small>
                </div>
                <div class="d-grid mt-3">
                    <a href="{{ url_for('view_client', id=order.client.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-2"></i>عرض العميل
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Warning -->
        <div class="alert alert-warning mt-3">
            <small>
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> تأكد من صحة البيانات قبل الحفظ. بعض التغييرات قد تؤثر على الفواتير المطبوعة.
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-calculate price based on pieces
document.addEventListener('DOMContentLoaded', function() {
    const piecesInput = document.getElementById('pieces_count');
    const priceInput = document.getElementById('total_price');
    
    piecesInput.addEventListener('input', function() {
        if (this.value) {
            // You can add auto-calculation logic here
            console.log('Pieces changed to:', this.value);
        }
    });
    
    // Focus on first input
    document.getElementById('fabric_type').focus();
});
</script>
{% endblock %}
