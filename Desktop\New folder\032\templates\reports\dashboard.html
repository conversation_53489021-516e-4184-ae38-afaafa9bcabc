{% extends "base.html" %}

{% block title %}التقارير - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات</h2>
    <div>
        <button class="btn btn-outline-primary" onclick="window.print()">
            <i class="fas fa-print me-2"></i>طباعة التقرير
        </button>
    </div>
</div>

<!-- Daily Report -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>تقرير اليوم</h5>
                <small class="text-muted">{{ daily_report.date.strftime('%Y-%m-%d') }}</small>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary">{{ daily_report.total_orders }}</h4>
                        <small class="text-muted">طلبات اليوم</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">{{ "%.2f"|format(daily_report.total_revenue) }} ريال</h4>
                        <small class="text-muted">إيرادات اليوم</small>
                    </div>
                </div>
                
                {% if daily_report.status_counts %}
                <hr>
                <h6>توزيع الطلبات حسب الحالة:</h6>
                <div class="row">
                    {% for status, count in daily_report.status_counts.items() %}
                    <div class="col-6 mb-2">
                        <span class="badge status-{{ status }} me-2">{{ count }}</span>
                        <small>
                            {% if status == 'new' %}جديد
                            {% elif status == 'ongoing' %}قيد التنفيذ
                            {% elif status == 'ready' %}جاهز
                            {% elif status == 'delivered' %}تم التسليم
                            {% endif %}
                        </small>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>تقرير الشهر</h5>
                <small class="text-muted">{{ monthly_report.year }}-{{ "%02d"|format(monthly_report.month) }}</small>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-info">{{ monthly_report.total_orders }}</h4>
                        <small class="text-muted">طلبات الشهر</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-warning">{{ "%.2f"|format(monthly_report.total_revenue) }} ريال</h4>
                        <small class="text-muted">إيرادات الشهر</small>
                    </div>
                </div>
                
                {% if monthly_report.fabric_counts %}
                <hr>
                <h6>أنواع الأقمشة الأكثر طلباً:</h6>
                <div class="row">
                    {% for fabric, count in monthly_report.fabric_counts.items() %}
                    <div class="col-6 mb-2">
                        <span class="badge bg-secondary me-2">{{ count }}</span>
                        <small>{{ fabric }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Top Clients -->
{% if monthly_report.top_clients %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>أفضل العملاء هذا الشهر</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>عدد الطلبات</th>
                                <th>إجمالي المبلغ</th>
                                <th>متوسط الطلب</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client_data in monthly_report.top_clients[:5] %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('view_client', id=client_data.client.id) }}" class="text-decoration-none">
                                        {{ client_data.client.full_name }}
                                    </a>
                                </td>
                                <td>{{ client_data.orders }}</td>
                                <td>{{ "%.2f"|format(client_data.revenue) }} ريال</td>
                                <td>{{ "%.2f"|format(client_data.revenue / client_data.orders) }} ريال</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Orders -->
{% if daily_report.orders %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>طلبات اليوم</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العميل</th>
                                <th>نوع القماش</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>الوقت</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in daily_report.orders %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('view_order', id=order.id) }}" class="text-decoration-none">
                                        #{{ order.id }}
                                    </a>
                                </td>
                                <td>{{ order.client.full_name }}</td>
                                <td>{{ order.fabric_type }}</td>
                                <td>{{ "%.2f"|format(order.total_price) }} ريال</td>
                                <td>
                                    <span class="badge status-{{ order.status }}">
                                        {{ order.get_status_display() }}
                                    </span>
                                </td>
                                <td>{{ order.order_date.strftime('%H:%M') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center text-muted py-5">
                <i class="fas fa-chart-line fa-3x mb-3"></i>
                <h5>لا توجد طلبات اليوم</h5>
                <p>لم يتم تسجيل أي طلبات اليوم</p>
                <a href="{{ url_for('add_order') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة طلب جديد
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh every 5 minutes
setTimeout(function() {
    location.reload();
}, 300000);

// Print styles
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style>
@media print {
    .btn, .navbar, .sidebar {
        display: none !important;
    }
    
    .main-content {
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        break-inside: avoid;
    }
}
</style>
{% endblock %}
