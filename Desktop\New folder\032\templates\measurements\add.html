{% extends "base.html" %}

{% block title %}إضافة مقاسات - {{ client.full_name }} - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-ruler me-2"></i>إضافة مقاسات - {{ client.full_name }}</h2>
    <a href="{{ url_for('view_client', id=client.id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للعميل
    </a>
</div>

<div class="row">
    <!-- Measurement Form -->
    <div class="col-md-9">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-ruler-combined me-2"></i>المقاسات المتقدمة</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}

                    <!-- Basic Measurements -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted mb-3 border-bottom pb-2">المقاسات الأساسية</h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.neck.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.neck(class="form-control" + (" is-invalid" if form.neck.errors else ""), step="0.1") }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.neck.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.neck.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.shoulder.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.shoulder(class="form-control" + (" is-invalid" if form.shoulder.errors else ""), step="0.1") }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.shoulder.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.shoulder.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.sleeve.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.sleeve(class="form-control" + (" is-invalid" if form.sleeve.errors else ""), step="0.1") }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.sleeve.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.sleeve.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.length.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.length(class="form-control" + (" is-invalid" if form.length.errors else ""), step="0.1") }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.length.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.length.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.chest_width.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.chest_width(class="form-control" + (" is-invalid" if form.chest_width.errors else ""), step="0.1") }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.chest_width.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.chest_width.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.zipper_length.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.zipper_length(class="form-control" + (" is-invalid" if form.zipper_length.errors else ""), step="0.1") }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.zipper_length.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.zipper_length.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.lower_width.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.lower_width(class="form-control" + (" is-invalid" if form.lower_width.errors else ""), step="0.1") }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.lower_width.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.lower_width.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.fabric_type.label(class="form-label") }}
                            {{ form.fabric_type(class="form-select" + (" is-invalid" if form.fabric_type.errors else "")) }}
                            {% if form.fabric_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.fabric_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.has_zhengvash(class="form-check-input" + (" is-invalid" if form.has_zhengvash.errors else "")) }}
                                {{ form.has_zhengvash.label(class="form-check-label") }}
                            </div>
                            {% if form.has_zhengvash.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.has_zhengvash.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="4") }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">أي ملاحظات خاصة بالمقاسات أو تفضيلات العميل</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ المقاسات
                        </button>
                        <a href="{{ url_for('view_client', id=client.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>


                </form>
            </div>
        </div>
    </div>

    <!-- Measurement Diagram -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-image me-2"></i>دليل المقاسات</h6>
            </div>
            <div class="card-body text-center">
                <div class="measurement-diagram">
                    <svg width="300" height="400" viewBox="0 0 300 400" class="border rounded">
                        <!-- Body outline -->
                        <path d="M150 50 L120 80 L120 120 L100 140 L100 300 L120 320 L180 320 L200 300 L200 140 L180 120 L180 80 Z"
                              fill="none" stroke="#667eea" stroke-width="2"/>

                        <!-- Neck -->
                        <circle cx="150" cy="40" r="15" fill="none" stroke="#667eea" stroke-width="2"/>
                        <text x="170" y="45" font-size="12" fill="#667eea">1. الرقبة</text>

                        <!-- Shoulder -->
                        <line x1="120" y1="80" x2="180" y2="80" stroke="#28a745" stroke-width="2"/>
                        <text x="185" y="85" font-size="12" fill="#28a745">2. الكتف</text>

                        <!-- Sleeve -->
                        <line x1="100" y1="140" x2="80" y2="200" stroke="#ffc107" stroke-width="2"/>
                        <text x="50" y="170" font-size="12" fill="#ffc107">3. الكم</text>

                        <!-- Length -->
                        <line x1="90" y1="80" x2="90" y2="320" stroke="#dc3545" stroke-width="2"/>
                        <text x="50" y="200" font-size="12" fill="#dc3545">4. الطول</text>

                        <!-- Chest -->
                        <line x1="120" y1="120" x2="180" y2="120" stroke="#17a2b8" stroke-width="2"/>
                        <text x="185" y="125" font-size="12" fill="#17a2b8">5. الصدر</text>

                        <!-- Zipper -->
                        <line x1="150" y1="80" x2="150" y2="140" stroke="#6f42c1" stroke-width="2"/>
                        <text x="155" y="110" font-size="12" fill="#6f42c1">6. السحاب</text>

                        <!-- Lower width -->
                        <line x1="120" y1="300" x2="180" y2="300" stroke="#fd7e14" stroke-width="2"/>
                        <text x="185" y="305" font-size="12" fill="#fd7e14">8. الأسفل</text>
                    </svg>
                </div>

                <div class="mt-3">
                    <small class="text-muted">
                        <strong>دليل المقاسات:</strong><br>
                        1. محيط الرقبة<br>
                        2. عرض الكتف<br>
                        3. طول الكم<br>
                        4. طول الثوب<br>
                        5. عرض الصدر<br>
                        6. طول السحاب<br>
                        7. زنجفاش (نعم/لا)<br>
                        8. عرض الأسفل
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-calculate some measurements based on others
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input[type="number"]');

    inputs.forEach(input => {
        input.addEventListener('input', function() {
            // Add visual feedback for filled measurements
            if (this.value) {
                this.classList.add('border-success');
            } else {
                this.classList.remove('border-success');
            }
        });
    });

    // Focus on first measurement input
    document.getElementById('neck').focus();
});
</script>
{% endblock %}
