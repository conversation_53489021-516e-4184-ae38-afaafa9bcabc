{% extends "base.html" %}

{% block title %}الطلبات - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-shopping-cart me-2"></i>إدارة الطلبات</h2>
    <a href="{{ url_for('add_order') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>طلب جديد
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <input type="text" class="form-control" name="search"
                       placeholder="البحث بالعميل..."
                       value="{{ search }}">
            </div>
            <div class="col-md-3">
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="new" {% if status == 'new' %}selected{% endif %}>جديد</option>
                    <option value="ongoing" {% if status == 'ongoing' %}selected{% endif %}>قيد التنفيذ</option>
                    <option value="ready" {% if status == 'ready' %}selected{% endif %}>جاهز</option>
                    <option value="delivered" {% if status == 'delivered' %}selected{% endif %}>تم التسليم</option>
                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>معلق</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="d-grid gap-2 d-md-flex">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                    <a href="{{ url_for('orders') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Orders Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة الطلبات ({{ orders.total }} طلب)</h5>
    </div>
    <div class="card-body">
        {% if orders.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>العميل</th>
                        <th>نوع القماش</th>
                        <th>عدد القطع</th>
                        <th>السعر</th>
                        <th>الحالة</th>
                        <th>تاريخ الطلب</th>
                        <th>التسليم المتوقع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in orders.items %}
                    <tr>
                        <td>
                            <a href="{{ url_for('view_order', id=order.id) }}"
                               class="text-decoration-none fw-bold">
                                #{{ order.id }}
                            </a>
                        </td>
                        <td>
                            <a href="{{ url_for('view_client', id=order.client.id) }}"
                               class="text-decoration-none">
                                {{ order.client.full_name }}
                            </a>
                        </td>
                        <td>{{ order.fabric_type }}</td>
                        <td>{{ order.pieces_count }}</td>
                        <td>{{ "%.2f"|format(order.total_price) }} ريال</td>
                        <td>
                            <span class="badge status-{{ order.status }}">
                                {{ order.get_status_display() }}
                            </span>
                        </td>
                        <td>{{ order.order_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if order.expected_delivery %}
                                {{ order.expected_delivery.strftime('%Y-%m-%d') }}
                                {% if order.expected_delivery < today and order.status not in ['delivered'] %}
                                    <i class="fas fa-exclamation-triangle text-warning ms-1" title="متأخر"></i>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('view_order', id=order.id) }}"
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_order', id=order.id) }}"
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if order.status in ['ready', 'delivered'] %}
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('view_invoice_html', id=order.id) }}"
                                       class="btn btn-outline-info" title="عرض الفاتورة" target="_blank">
                                        <i class="fas fa-file-invoice"></i>
                                    </a>
                                    <a href="{{ url_for('generate_invoice', id=order.id) }}"
                                       class="btn btn-outline-success" title="تحميل PDF" target="_blank">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if orders.pages > 1 %}
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if orders.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('orders', page=orders.prev_num, search=search, status=status) }}">
                        السابق
                    </a>
                </li>
                {% endif %}

                {% for page_num in orders.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != orders.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('orders', page=page_num, search=search, status=status) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if orders.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('orders', page=orders.next_num, search=search, status=status) }}">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center text-muted py-5">
            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
            <h5>لا توجد طلبات</h5>
            <p>لم يتم العثور على أي طلبات{% if search or status %} تطابق المعايير المحددة{% endif %}</p>
            <a href="{{ url_for('add_order') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة أول طلب
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-primary">{{ orders.items|selectattr("status", "equalto", "new")|list|length }}</h5>
                <small class="text-muted">طلبات جديدة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-warning">{{ orders.items|selectattr("status", "equalto", "ongoing")|list|length }}</h5>
                <small class="text-muted">قيد التنفيذ</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-success">{{ orders.items|selectattr("status", "equalto", "ready")|list|length }}</h5>
                <small class="text-muted">جاهزة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-secondary">{{ orders.items|selectattr("status", "equalto", "delivered")|list|length }}</h5>
                <small class="text-muted">تم التسليم</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh every 30 seconds for real-time updates
setTimeout(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}
