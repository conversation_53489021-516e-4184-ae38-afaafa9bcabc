from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='operator')  # admin, operator, viewer
    is_active = db.Column(db.Bo<PERSON>an, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

class Client(db.Model):
    __tablename__ = 'clients'

    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(100), nullable=False)
    mobile_number = db.Column(db.String(20), nullable=False)
    card_number = db.Column(db.String(50), nullable=True)
    address = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    measurements = db.relationship('Measurement', backref='client', lazy=True, cascade='all, delete-orphan')
    orders = db.relationship('Order', backref='client', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Client {self.full_name}>'

class Measurement(db.Model):
    __tablename__ = 'measurements'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)

    # Measurements in centimeters
    neck = db.Column(db.Float, nullable=True)  # محيط الرقبة
    shoulder = db.Column(db.Float, nullable=True)  # عرض الكتف
    sleeve = db.Column(db.Float, nullable=True)  # طول الكم
    length = db.Column(db.Float, nullable=True)  # طول الثوب
    chest_width = db.Column(db.Float, nullable=True)  # عرض الصدر
    zipper_length = db.Column(db.Float, nullable=True)  # طول السحاب
    has_zhengvash = db.Column(db.Boolean, default=False)  # يحتوي على زنجفاش
    lower_width = db.Column(db.Float, nullable=True)  # عرض الأسفل
    fabric_type = db.Column(db.String(50), nullable=True)  # نوع القماش

    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Measurement for Client {self.client_id}>'

class Order(db.Model):
    __tablename__ = 'orders'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)

    order_date = db.Column(db.DateTime, default=datetime.utcnow)
    fabric_type = db.Column(db.String(50), nullable=False)
    pieces_count = db.Column(db.Integer, default=1)
    total_price = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='new')  # new, ongoing, ready, delivered
    expected_delivery = db.Column(db.Date, nullable=True)
    actual_delivery = db.Column(db.Date, nullable=True)

    # Additional details
    fabric_photo_path = db.Column(db.String(255), nullable=True)
    special_requests = db.Column(db.Text, nullable=True)
    notes = db.Column(db.Text, nullable=True)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Order {self.id} for Client {self.client_id}>'

    def get_status_display(self):
        status_map = {
            'new': 'جديد',
            'ongoing': 'قيد التنفيذ',
            'ready': 'جاهز',
            'delivered': 'تم التسليم'
        }
        return status_map.get(self.status, self.status)
