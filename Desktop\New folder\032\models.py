from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    role = db.Column(db.String(20), default='operator')  # admin, operator, viewer
    is_active = db.Column(db.<PERSON>, default=True)

    # Notification preferences
    email_notifications = db.Column(db.<PERSON>, default=True)
    sms_notifications = db.Column(db.<PERSON>, default=True)
    desktop_notifications = db.Column(db.<PERSON><PERSON>, default=True)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

class Client(db.Model):
    __tablename__ = 'clients'

    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(100), nullable=False)
    mobile_number = db.Column(db.String(20), nullable=False)
    address = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    measurements = db.relationship('Measurement', backref='client', lazy=True, cascade='all, delete-orphan')
    orders = db.relationship('Order', backref='client', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Client {self.full_name}>'

class Measurement(db.Model):
    __tablename__ = 'measurements'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)

    # Measurements in centimeters (existing fields)
    neck = db.Column(db.Float, nullable=True)  # محيط الرقبة
    shoulder = db.Column(db.Float, nullable=True)  # عرض الكتف
    sleeve = db.Column(db.Float, nullable=True)  # طول الكم
    length = db.Column(db.Float, nullable=True)  # طول الثوب
    chest_width = db.Column(db.Float, nullable=True)  # عرض الصدر
    zipper_length = db.Column(db.Float, nullable=True)  # طول السحاب
    has_zhengvash = db.Column(db.Boolean, default=False)  # يحتوي على زنجفاش
    lower_width = db.Column(db.Float, nullable=True)  # عرض الأسفل
    fabric_type = db.Column(db.String(50), nullable=True)  # نوع القماش

    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Measurement for Client {self.client_id}>'

class Order(db.Model):
    __tablename__ = 'orders'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)

    order_date = db.Column(db.DateTime, default=datetime.utcnow)
    fabric_type = db.Column(db.String(50), nullable=False)
    pieces_count = db.Column(db.Integer, default=1)
    is_rush_order = db.Column(db.Boolean, default=False)
    total_price = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='new')  # new, ongoing, ready, delivered
    expected_delivery = db.Column(db.Date, nullable=True)
    actual_delivery = db.Column(db.Date, nullable=True)

    # Additional details
    fabric_photo_path = db.Column(db.String(255), nullable=True)
    special_requests = db.Column(db.Text, nullable=True)
    notes = db.Column(db.Text, nullable=True)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Order {self.id} for Client {self.client_id}>'

    def get_status_display(self):
        status_map = {
            'new': 'جديد',
            'ongoing': 'قيد التنفيذ',
            'ready': 'جاهز',
            'delivered': 'تم التسليم'
        }
        return status_map.get(self.status, self.status)

    def get_status_color(self):
        color_map = {
            'new': 'primary',
            'ongoing': 'warning',
            'ready': 'success',
            'delivered': 'secondary'
        }
        return color_map.get(self.status, 'secondary')

    def can_change_to_status(self, new_status):
        """Check if status change is allowed"""
        status_flow = {
            'new': ['ongoing', 'ready'],
            'ongoing': ['ready', 'new'],
            'ready': ['delivered', 'ongoing'],
            'delivered': []  # Cannot change from delivered
        }
        return new_status in status_flow.get(self.status, [])

    def calculate_price(self):
        """Calculate price based on pricing rules"""
        pricing_rule = PricingRule.query.filter_by(fabric_type=self.fabric_type, is_active=True).first()
        if not pricing_rule:
            return 0.0

        # Base price calculation
        base_price = pricing_rule.base_price * self.pieces_count

        # Apply size multiplier (can be enhanced later with client size data)
        price_with_size = base_price * pricing_rule.size_multiplier

        # Apply rush order multiplier if applicable
        if self.is_rush_order:
            price_with_rush = price_with_size * pricing_rule.rush_multiplier
        else:
            price_with_rush = price_with_size

        # Apply bulk discount if applicable
        bulk_threshold = SystemSettings.query.filter_by(setting_key='bulk_discount_threshold').first()
        bulk_discount_pct = SystemSettings.query.filter_by(setting_key='bulk_discount_percentage').first()

        if (bulk_threshold and bulk_discount_pct and
            self.pieces_count >= int(bulk_threshold.get_value())):
            discount = price_with_rush * (float(bulk_discount_pct.get_value()) / 100)
            price_with_discount = price_with_rush - discount
        else:
            price_with_discount = price_with_rush

        # Apply tax if enabled
        tax_enabled = SystemSettings.query.filter_by(setting_key='tax_enabled').first()
        tax_percentage = SystemSettings.query.filter_by(setting_key='tax_percentage').first()

        if (tax_enabled and tax_enabled.get_value() and tax_percentage):
            tax = price_with_discount * (float(tax_percentage.get_value()) / 100)
            final_price = price_with_discount + tax
        else:
            final_price = price_with_discount

        return round(final_price, 2)

    def get_pricing_breakdown(self):
        """Get detailed pricing breakdown"""
        pricing_rule = PricingRule.query.filter_by(fabric_type=self.fabric_type, is_active=True).first()
        if not pricing_rule:
            return None

        breakdown = {
            'base_price_per_piece': pricing_rule.base_price,
            'pieces_count': self.pieces_count,
            'base_total': pricing_rule.base_price * self.pieces_count,
            'size_multiplier': pricing_rule.size_multiplier,
            'price_with_size': pricing_rule.base_price * self.pieces_count * pricing_rule.size_multiplier,
            'is_rush_order': self.is_rush_order,
            'rush_multiplier': pricing_rule.rush_multiplier if self.is_rush_order else 1.0,
            'price_with_rush': 0,
            'bulk_discount_applied': False,
            'bulk_discount_amount': 0,
            'price_after_discount': 0,
            'tax_applied': False,
            'tax_amount': 0,
            'final_price': 0
        }

        # Calculate step by step
        price_with_size = breakdown['price_with_size']

        if self.is_rush_order:
            breakdown['price_with_rush'] = price_with_size * pricing_rule.rush_multiplier
        else:
            breakdown['price_with_rush'] = price_with_size

        # Check bulk discount
        bulk_threshold = SystemSettings.query.filter_by(setting_key='bulk_discount_threshold').first()
        bulk_discount_pct = SystemSettings.query.filter_by(setting_key='bulk_discount_percentage').first()

        if (bulk_threshold and bulk_discount_pct and
            self.pieces_count >= int(bulk_threshold.get_value())):
            breakdown['bulk_discount_applied'] = True
            breakdown['bulk_discount_amount'] = breakdown['price_with_rush'] * (float(bulk_discount_pct.get_value()) / 100)
            breakdown['price_after_discount'] = breakdown['price_with_rush'] - breakdown['bulk_discount_amount']
        else:
            breakdown['price_after_discount'] = breakdown['price_with_rush']

        # Check tax
        tax_enabled = SystemSettings.query.filter_by(setting_key='tax_enabled').first()
        tax_percentage = SystemSettings.query.filter_by(setting_key='tax_percentage').first()

        if (tax_enabled and tax_enabled.get_value() and tax_percentage):
            breakdown['tax_applied'] = True
            breakdown['tax_amount'] = breakdown['price_after_discount'] * (float(tax_percentage.get_value()) / 100)
            breakdown['final_price'] = breakdown['price_after_discount'] + breakdown['tax_amount']
        else:
            breakdown['final_price'] = breakdown['price_after_discount']

        return breakdown

class OrderStatusHistory(db.Model):
    __tablename__ = 'order_status_history'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False)
    old_status = db.Column(db.String(20), nullable=True)
    new_status = db.Column(db.String(20), nullable=False)
    changed_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    change_reason = db.Column(db.Text, nullable=True)
    changed_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    order = db.relationship('Order', backref='status_history')
    user = db.relationship('User', backref='status_changes')

    def __repr__(self):
        return f'<StatusChange {self.old_status} -> {self.new_status}>'

class PricingRule(db.Model):
    __tablename__ = 'pricing_rules'

    id = db.Column(db.Integer, primary_key=True)
    fabric_type = db.Column(db.String(50), nullable=False)
    base_price = db.Column(db.Float, nullable=False)
    size_multiplier = db.Column(db.Float, default=1.0)  # Multiplier based on size
    rush_multiplier = db.Column(db.Float, default=1.5)  # Rush order multiplier
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<PricingRule {self.fabric_type}: {self.base_price}>'

class SystemSettings(db.Model):
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True)
    setting_key = db.Column(db.String(100), unique=True, nullable=False)
    setting_value = db.Column(db.Text, nullable=True)
    setting_type = db.Column(db.String(20), default='text')  # text, number, boolean, json
    description = db.Column(db.Text, nullable=True)
    category = db.Column(db.String(50), default='general')
    is_editable = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_value(self):
        """Get the actual value based on type"""
        if self.setting_type == 'boolean':
            return self.setting_value.lower() in ['true', '1', 'yes']
        elif self.setting_type == 'number':
            try:
                return float(self.setting_value)
            except (ValueError, TypeError):
                return 0
        elif self.setting_type == 'json':
            try:
                import json
                return json.loads(self.setting_value)
            except (ValueError, TypeError):
                return {}
        return self.setting_value

    def set_value(self, value):
        """Set value with proper type conversion"""
        if self.setting_type == 'boolean':
            self.setting_value = str(bool(value)).lower()
        elif self.setting_type == 'number':
            self.setting_value = str(float(value))
        elif self.setting_type == 'json':
            import json
            self.setting_value = json.dumps(value)
        else:
            self.setting_value = str(value)

    def __repr__(self):
        return f'<SystemSettings {self.setting_key}: {self.setting_value}>'

class Notification(db.Model):
    __tablename__ = 'notifications'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(50), default='info')  # info, success, warning, error
    category = db.Column(db.String(50), default='general')  # order, client, system, etc.
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Optional reference to related objects
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=True)

    # Relationships
    user = db.relationship('User', backref='notifications')
    order = db.relationship('Order', backref='notifications')
    client = db.relationship('Client', backref='notifications')

    def __repr__(self):
        return f'<Notification {self.title} for User {self.user_id}>'

    def mark_as_read(self):
        """Mark notification as read"""
        self.is_read = True
        db.session.commit()

    def get_icon(self):
        """Get icon based on notification type"""
        icons = {
            'info': 'fas fa-info-circle',
            'success': 'fas fa-check-circle',
            'warning': 'fas fa-exclamation-triangle',
            'error': 'fas fa-times-circle',
            'order': 'fas fa-shopping-cart',
            'client': 'fas fa-user',
            'system': 'fas fa-cog'
        }
        return icons.get(self.type, 'fas fa-bell')

    def get_color(self):
        """Get color based on notification type"""
        colors = {
            'info': 'primary',
            'success': 'success',
            'warning': 'warning',
            'error': 'danger'
        }
        return colors.get(self.type, 'secondary')
