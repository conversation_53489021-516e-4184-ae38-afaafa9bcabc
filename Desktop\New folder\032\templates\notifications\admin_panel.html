{% extends "base.html" %}

{% block title %}لوحة تحكم الإشعارات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bell-slash me-2"></i>لوحة تحكم الإشعارات</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('notifications') }}">الإشعارات</a></li>
                        <li class="breadcrumb-item active">لوحة التحكم</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- System Checks -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-search me-2"></i>فحص النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <button type="button" class="btn btn-warning" onclick="checkSystemNotifications()">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    فحص الطلبات المتأخرة والتذكيرات
                                </button>
                            </div>
                            <small class="text-muted mt-2 d-block">
                                يفحص الطلبات المتأخرة والطلبات المستحقة غداً والطلبات الجاهزة للاستلام
                            </small>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <button type="button" class="btn btn-info" onclick="generateDailySummary()">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    إرسال الملخص اليومي
                                </button>
                            </div>
                            <small class="text-muted mt-2 d-block">
                                يرسل ملخص أداء اليوم للمديرين
                            </small>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <button type="button" class="btn btn-success" onclick="sendCustomNotification()">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال إشعار مخصص
                                </button>
                            </div>
                            <small class="text-muted mt-2 d-block">
                                إرسال إشعار مخصص لجميع المستخدمين أو مستخدمين محددين
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="total-notifications">{{ total_notifications }}</h4>
                            <p class="mb-0">إجمالي الإشعارات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bell fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="unread-notifications">{{ unread_notifications }}</h4>
                            <p class="mb-0">غير مقروءة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="today-notifications">{{ today_notifications }}</h4>
                            <p class="mb-0">اليوم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="active-users">{{ active_users }}</h4>
                            <p class="mb-0">مستخدمين نشطين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent System Notifications -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>الإشعارات الحديثة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>العنوان</th>
                                    <th>الفئة</th>
                                    <th>المستخدمين</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody id="recent-notifications-table">
                                {% for notification in recent_notifications %}
                                <tr>
                                    <td>
                                        <i class="{{ notification.get_icon() }} text-{{ notification.get_color() }}"></i>
                                    </td>
                                    <td>{{ notification.title }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ notification.category }}</span>
                                    </td>
                                    <td>
                                        {% if notification.order_id %}
                                        <a href="{{ url_for('view_order', id=notification.order_id) }}" class="btn btn-sm btn-outline-primary">
                                            طلب #{{ notification.order_id }}
                                        </a>
                                        {% endif %}
                                        {% if notification.client_id %}
                                        <a href="{{ url_for('view_client', id=notification.client_id) }}" class="btn btn-sm btn-outline-info">
                                            عميل
                                        </a>
                                        {% endif %}
                                    </td>
                                    <td>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if notification.is_read %}
                                        <span class="badge bg-success">مقروء</span>
                                        {% else %}
                                        <span class="badge bg-warning">غير مقروء</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Notification Modal -->
<div class="modal fade" id="customNotificationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال إشعار مخصص</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customNotificationForm">
                    <div class="mb-3">
                        <label for="notificationTitle" class="form-label">عنوان الإشعار</label>
                        <input type="text" class="form-control" id="notificationTitle" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notificationMessage" class="form-label">رسالة الإشعار</label>
                        <textarea class="form-control" id="notificationMessage" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notificationType" class="form-label">نوع الإشعار</label>
                        <select class="form-select" id="notificationType">
                            <option value="info">معلومات</option>
                            <option value="success">نجاح</option>
                            <option value="warning">تحذير</option>
                            <option value="error">خطأ</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notificationRecipients" class="form-label">المستقبلين</label>
                        <select class="form-select" id="notificationRecipients">
                            <option value="all">جميع المستخدمين</option>
                            <option value="admins">المديرين فقط</option>
                            <option value="operators">المشغلين فقط</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitCustomNotification()">إرسال</button>
            </div>
        </div>
    </div>
</div>

<script>
function checkSystemNotifications() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الفحص...';
    btn.disabled = true;
    
    fetch('/api/notifications/check-system', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrf_token]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'حدث خطأ في الاتصال');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function generateDailySummary() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    btn.disabled = true;
    
    fetch('/api/notifications/daily-summary', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrf_token]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'حدث خطأ في الاتصال');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function sendCustomNotification() {
    const modal = new bootstrap.Modal(document.getElementById('customNotificationModal'));
    modal.show();
}

function submitCustomNotification() {
    const title = document.getElementById('notificationTitle').value;
    const message = document.getElementById('notificationMessage').value;
    const type = document.getElementById('notificationType').value;
    const recipients = document.getElementById('notificationRecipients').value;
    
    if (!title || !message) {
        showAlert('error', 'يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    fetch('/api/notifications/custom', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrf_token]').value
        },
        body: JSON.stringify({
            title: title,
            message: message,
            type: type,
            recipients: recipients
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('customNotificationModal')).hide();
            document.getElementById('customNotificationForm').reset();
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'حدث خطأ في الاتصال');
    });
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
