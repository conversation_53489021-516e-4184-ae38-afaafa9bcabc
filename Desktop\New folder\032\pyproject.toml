[tool.pylance]
include = ["."]
exclude = ["**/__pycache__"]
reportMissingImports = "warning"
reportMissingModuleSource = "warning"
pythonVersion = "3.11"

[tool.pyright]
include = ["."]
exclude = ["**/__pycache__"]
reportMissingImports = "warning"
reportMissingModuleSource = "warning"
pythonVersion = "3.11"
pythonPlatform = "Windows"

[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"
