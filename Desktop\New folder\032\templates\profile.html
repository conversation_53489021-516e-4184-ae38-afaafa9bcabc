{% extends "base.html" %}

{% block title %}الملف الشخصي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-circle me-2"></i>الملف الشخصي</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">الملف الشخصي</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-edit me-2"></i>تحديث المعلومات الشخصية</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.full_name.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                                </div>
                                {% if form.full_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.full_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.email.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                </div>
                                {% if form.email.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.email.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.phone.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-phone"></i>
                                    </span>
                                    {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                </div>
                                {% if form.phone.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <hr>
                        
                        <!-- Password Change -->
                        <h6 class="text-muted mb-3"><i class="fas fa-lock me-2"></i>تغيير كلمة المرور</h6>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.current_password.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-key"></i>
                                    </span>
                                    {{ form.current_password(class="form-control" + (" is-invalid" if form.current_password.errors else "")) }}
                                </div>
                                {% if form.current_password.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.current_password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                {{ form.new_password.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    {{ form.new_password(class="form-control" + (" is-invalid" if form.new_password.errors else "")) }}
                                </div>
                                {% if form.new_password.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.new_password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                {{ form.confirm_password.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    {{ form.confirm_password(class="form-control" + (" is-invalid" if form.confirm_password.errors else "")) }}
                                </div>
                                {% if form.confirm_password.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.confirm_password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <hr>
                        
                        <!-- Notification Preferences -->
                        <h6 class="text-muted mb-3"><i class="fas fa-bell me-2"></i>إعدادات الإشعارات</h6>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch">
                                    {{ form.email_notifications(class="form-check-input") }}
                                    {{ form.email_notifications.label(class="form-check-label") }}
                                </div>
                                <small class="text-muted">استقبال إشعارات عبر البريد الإلكتروني</small>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch">
                                    {{ form.sms_notifications(class="form-check-input") }}
                                    {{ form.sms_notifications.label(class="form-check-label") }}
                                </div>
                                <small class="text-muted">استقبال إشعارات عبر الرسائل النصية</small>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch">
                                    {{ form.desktop_notifications(class="form-check-input") }}
                                    {{ form.desktop_notifications.label(class="form-check-label") }}
                                </div>
                                <small class="text-muted">إشعارات سطح المكتب في المتصفح</small>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Profile Summary -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الحساب</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-circle bg-primary text-white mb-3">
                            <i class="fas fa-user fa-3x"></i>
                        </div>
                        <h5>{{ current_user.full_name }}</h5>
                        <p class="text-muted">{{ current_user.username }}</p>
                    </div>
                    
                    <hr>
                    
                    <div class="info-item mb-2">
                        <strong>الدور:</strong>
                        <span class="badge bg-primary">
                            {% if current_user.role == 'admin' %}
                                مدير
                            {% elif current_user.role == 'operator' %}
                                مشغل
                            {% else %}
                                مشاهد
                            {% endif %}
                        </span>
                    </div>
                    
                    <div class="info-item mb-2">
                        <strong>تاريخ التسجيل:</strong><br>
                        <small class="text-muted">{{ current_user.created_at.strftime('%Y-%m-%d') }}</small>
                    </div>
                    
                    {% if current_user.last_login %}
                    <div class="info-item mb-2">
                        <strong>آخر دخول:</strong><br>
                        <small class="text-muted">{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    {% endif %}
                    
                    <div class="info-item mb-2">
                        <strong>حالة الحساب:</strong>
                        {% if current_user.is_active %}
                            <span class="badge bg-success">نشط</span>
                        {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('notifications') }}" class="btn btn-outline-primary">
                            <i class="fas fa-bell me-2"></i>عرض الإشعارات
                        </a>
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-info">
                            <i class="fas fa-chart-bar me-2"></i>لوحة التحكم
                        </a>
                        {% if current_user.is_admin() %}
                        <a href="{{ url_for('system_settings') }}" class="btn btn-outline-warning">
                            <i class="fas fa-cog me-2"></i>إعدادات النظام
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.info-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}
