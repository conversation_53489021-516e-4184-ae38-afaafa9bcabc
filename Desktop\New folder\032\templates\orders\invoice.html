<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة طلب #{{ order.id }} - نظام إدارة خياطة الرجال القطري</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: #f8f9fa;
            direction: rtl;
            text-align: right;
        }

        .invoice-container {
            max-width: 800px;
            margin: 2rem auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .invoice-header {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .company-logo {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .company-name {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .company-info {
            font-size: 1rem;
            opacity: 0.9;
        }

        .invoice-title {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            border-bottom: 3px solid #8B0000;
        }

        .invoice-title h2 {
            color: #8B0000;
            font-weight: 700;
            margin: 0;
            font-size: 2rem;
        }

        .invoice-body {
            padding: 2rem;
        }

        .info-section {
            margin-bottom: 2rem;
        }

        .info-title {
            color: #8B0000;
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid #8B0000;
            padding-bottom: 0.5rem;
        }

        .info-table {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
        }

        .info-value {
            color: #212529;
        }

        .order-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .order-table th {
            background: #8B0000;
            color: white;
            padding: 1rem;
            text-align: center;
            font-weight: 600;
        }

        .order-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }

        .order-table tbody tr:hover {
            background: #f8f9fa;
        }

        .total-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            font-size: 1.1rem;
        }

        .total-final {
            border-top: 2px solid #8B0000;
            padding-top: 1rem;
            margin-top: 1rem;
            font-weight: 700;
            font-size: 1.3rem;
            color: #8B0000;
        }

        .notes-section {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
            border-right: 4px solid #2196f3;
        }

        .footer {
            background: #8B0000;
            color: white;
            text-align: center;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .print-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }

        @media print {
            .print-btn {
                display: none;
            }

            .invoice-container {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }

            body {
                background: white;
            }
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-ready {
            background: #d4edda;
            color: #155724;
        }

        .status-delivered {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <button class="btn btn-primary print-btn" onclick="window.print()">
        <i class="fas fa-print me-2"></i>طباعة
    </button>

    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="company-logo">
                <i class="fas fa-cut"></i>
            </div>
            <div class="company-name">نظام إدارة خياطة الرجال القطري</div>
            <div class="company-info">
                الدوحة، قطر | هاتف: +974 XXXX XXXX<br>
                البريد الإلكتروني: <EMAIL>
            </div>
        </div>

        <!-- Invoice Title -->
        <div class="invoice-title">
            <h2><i class="fas fa-file-invoice me-3"></i>فاتورة</h2>
        </div>

        <!-- Invoice Body -->
        <div class="invoice-body">
            <!-- Invoice Information -->
            <div class="info-section">
                <div class="info-title">
                    <i class="fas fa-info-circle me-2"></i>معلومات الفاتورة
                </div>
                <div class="info-table">
                    <div class="info-row">
                        <span class="info-label">رقم الفاتورة:</span>
                        <span class="info-value">#{{ order.id }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">تاريخ الفاتورة:</span>
                        <span class="info-value">{{ now.strftime('%Y-%m-%d') }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">تاريخ الطلب:</span>
                        <span class="info-value">{{ order.order_date.strftime('%Y-%m-%d') }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">حالة الطلب:</span>
                        <span class="info-value">
                            <span class="status-badge status-{{ order.status }}">
                                {{ order.get_status_display() }}
                            </span>
                        </span>
                    </div>
                    {% if order.expected_delivery %}
                    <div class="info-row">
                        <span class="info-label">تاريخ التسليم المتوقع:</span>
                        <span class="info-value">{{ order.expected_delivery.strftime('%Y-%m-%d') }}</span>
                    </div>
                    {% endif %}
                    {% if order.actual_delivery %}
                    <div class="info-row">
                        <span class="info-label">تاريخ التسليم الفعلي:</span>
                        <span class="info-value">{{ order.actual_delivery.strftime('%Y-%m-%d') }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Client Information -->
            <div class="info-section">
                <div class="info-title">
                    <i class="fas fa-user me-2"></i>معلومات العميل
                </div>
                <div class="info-table">
                    <div class="info-row">
                        <span class="info-label">اسم العميل:</span>
                        <span class="info-value">{{ order.client.full_name }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">رقم الجوال:</span>
                        <span class="info-value">{{ order.client.mobile_number }}</span>
                    </div>
                    {% if order.client.address %}
                    <div class="info-row">
                        <span class="info-label">العنوان:</span>
                        <span class="info-value">{{ order.client.address }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Order Details -->
            <div class="info-section">
                <div class="info-title">
                    <i class="fas fa-shopping-cart me-2"></i>تفاصيل الطلب
                </div>

                <table class="order-table">
                    <thead>
                        <tr>
                            <th>الوصف</th>
                            <th>الكمية</th>
                            <th>السعر للوحدة</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ order.fabric_type }}</td>
                            <td>{{ order.pieces_count }}</td>
                            <td>{{ "%.2f"|format(order.total_price / order.pieces_count) }} ريال</td>
                            <td>{{ "%.2f"|format(order.total_price) }} ريال</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Total Section -->
            <div class="total-section">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>{{ "%.2f"|format(order.total_price) }} ريال</span>
                </div>
                <div class="total-row">
                    <span>الضريبة (15%):</span>
                    <span>{{ "%.2f"|format(order.total_price * 0.15) }} ريال</span>
                </div>
                <div class="total-row total-final">
                    <span>المجموع الكلي:</span>
                    <span>{{ "%.2f"|format(order.total_price * 1.15) }} ريال</span>
                </div>
            </div>

            <!-- Special Requests and Notes -->
            {% if order.special_requests or order.notes %}
            <div class="notes-section">
                {% if order.special_requests %}
                <div class="mb-3">
                    <strong><i class="fas fa-star me-2"></i>طلبات خاصة:</strong><br>
                    {{ order.special_requests }}
                </div>
                {% endif %}

                {% if order.notes %}
                <div>
                    <strong><i class="fas fa-sticky-note me-2"></i>ملاحظات:</strong><br>
                    {{ order.notes|replace('\n', '<br>')|safe }}
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Footer -->
        <div class="footer">
            <h4><i class="fas fa-heart me-2"></i>شكراً لتعاملكم معنا</h4>
            <p>نتطلع لخدمتكم مرة أخرى</p>
        </div>
    </div>

    <script>
        // Auto-print functionality
        function autoPrint() {
            if (window.location.search.includes('print=true')) {
                setTimeout(() => {
                    window.print();
                }, 1000);
            }
        }

        // Call auto-print when page loads
        window.addEventListener('load', autoPrint);
    </script>
</body>
</html>
