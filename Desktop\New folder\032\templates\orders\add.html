{% extends "base.html" %}

{% block title %}طلب جديد - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-plus-circle me-2"></i>إضافة طلب جديد</h2>
    <a href="{{ url_for('orders') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للطلبات
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>تفاصيل الطلب</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.client_id.label(class="form-label") }}
                            {{ form.client_id(class="form-select" + (" is-invalid" if form.client_id.errors else "")) }}
                            {% if form.client_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.client_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                <a href="{{ url_for('add_client') }}" class="text-decoration-none">
                                    <i class="fas fa-user-plus me-1"></i>إضافة عميل جديد
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.fabric_type.label(class="form-label") }}
                            {{ form.fabric_type(class="form-select" + (" is-invalid" if form.fabric_type.errors else "")) }}
                            {% if form.fabric_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.fabric_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 mb-3">
                            {{ form.pieces_count.label(class="form-label") }}
                            {{ form.pieces_count(class="form-control" + (" is-invalid" if form.pieces_count.errors else ""), id="pieces_count") }}
                            {% if form.pieces_count.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.pieces_count.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-3 mb-3">
                            <label class="form-label">نوع الطلب</label>
                            <div class="form-check mt-2">
                                {{ form.is_rush_order(class="form-check-input", id="is_rush_order") }}
                                {{ form.is_rush_order.label(class="form-check-label") }}
                            </div>
                            <div class="form-text">رسوم إضافية للطلبات العاجلة</div>
                        </div>

                        <div class="col-md-3 mb-3">
                            {{ form.total_price.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.total_price(class="form-control" + (" is-invalid" if form.total_price.errors else ""), step="0.01", id="total_price", readonly=true) }}
                                <span class="input-group-text">ريال</span>
                            </div>
                            {% if form.total_price.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.total_price.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">يتم الحساب تلقائياً</div>
                        </div>

                        <div class="col-md-3 mb-3">
                            {{ form.expected_delivery.label(class="form-label") }}
                            {{ form.expected_delivery(class="form-control" + (" is-invalid" if form.expected_delivery.errors else ""), id="expected_delivery") }}
                            {% if form.expected_delivery.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.expected_delivery.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">يتم التحديد تلقائياً</div>
                        </div>
                    </div>

                    <!-- Price Breakdown -->
                    <div class="row" id="price-breakdown" style="display: none;">
                        <div class="col-12 mb-3">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>تفاصيل السعر</h6>
                                </div>
                                <div class="card-body" id="breakdown-content">
                                    <!-- Will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.fabric_photo.label(class="form-label") }}
                        {{ form.fabric_photo(class="form-control" + (" is-invalid" if form.fabric_photo.errors else "")) }}
                        {% if form.fabric_photo.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.fabric_photo.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">يمكن رفع صورة للقماش (اختياري)</div>
                    </div>

                    <div class="mb-3">
                        {{ form.special_requests.label(class="form-label") }}
                        {{ form.special_requests(class="form-control" + (" is-invalid" if form.special_requests.errors else ""), rows="3") }}
                        {% if form.special_requests.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.special_requests.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="3") }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('orders') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Order Guidelines -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>إرشادات الطلب</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">أنواع الأقمشة:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>قطن - مناسب للصيف</li>
                        <li><i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>صوف - مناسب للشتاء</li>
                        <li><i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>حرير - للمناسبات</li>
                        <li><i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>كتان - خفيف ومريح</li>
                        <li><i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>مخلوط - متعدد الاستخدام</li>
                    </ul>
                </div>

                <div class="mb-3">
                    <h6 class="text-success">الأسعار المقترحة:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-money-bill text-success me-2"></i>ثوب عادي: 200-300 ريال</li>
                        <li><i class="fas fa-money-bill text-success me-2"></i>ثوب فاخر: 400-600 ريال</li>
                        <li><i class="fas fa-money-bill text-success me-2"></i>ثوب حرير: 600-800 ريال</li>
                    </ul>
                </div>

                <div class="mb-3">
                    <h6 class="text-warning">مدة التنفيذ:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-clock text-warning me-2"></i>عادي: 7-10 أيام</li>
                        <li><i class="fas fa-clock text-warning me-2"></i>مستعجل: 3-5 أيام</li>
                        <li><i class="fas fa-clock text-warning me-2"></i>فاخر: 10-14 يوم</li>
                    </ul>
                </div>

                <div class="alert alert-info">
                    <small>
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>نصيحة:</strong> تأكد من وجود مقاسات محفوظة للعميل قبل إنشاء الطلب
                    </small>
                </div>
            </div>
        </div>

        <!-- Quick Client Info -->
        <div class="card mt-3" id="client-info" style="display: none;">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل</h6>
            </div>
            <div class="card-body" id="client-details">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fabricSelect = document.getElementById('fabric_type');
    const piecesInput = document.getElementById('pieces_count');
    const rushCheckbox = document.getElementById('is_rush_order');
    const priceInput = document.getElementById('total_price');
    const deliveryInput = document.getElementById('expected_delivery');
    const clientSelect = document.getElementById('client_id');

    // Calculate price automatically
    function calculatePrice() {
        const fabricType = fabricSelect.value;
        const pieces = parseInt(piecesInput.value) || 1;
        const isRush = rushCheckbox.checked;

        if (!fabricType) {
            priceInput.value = '';
            document.getElementById('price-breakdown').style.display = 'none';
            return;
        }

        // Call API to calculate price
        fetch('{{ url_for("calculate_price_api") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrf_token]').value
            },
            body: JSON.stringify({
                fabric_type: fabricType,
                pieces_count: pieces,
                is_rush_order: isRush
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error:', data.error);
                return;
            }

            priceInput.value = data.total_price.toFixed(2);

            if (data.breakdown) {
                displayPriceBreakdown(data.breakdown);
            }
        })
        .catch(error => {
            console.error('Error calculating price:', error);
        });
    }

    // Display price breakdown
    function displayPriceBreakdown(breakdown) {
        const content = document.getElementById('breakdown-content');

        let html = `
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">السعر الأساسي:</small><br>
                    <span>${breakdown.base_price_per_piece.toFixed(2)} ريال × ${breakdown.pieces_count} قطعة = ${breakdown.base_total.toFixed(2)} ريال</span>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">مضاعف الحجم:</small><br>
                    <span>${breakdown.price_with_size.toFixed(2)} ريال (${breakdown.size_multiplier}x)</span>
                </div>
            </div>
            <hr class="my-2">
        `;

        if (breakdown.is_rush_order) {
            html += `
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">رسوم الطلب العاجل:</small><br>
                        <span class="text-warning">${breakdown.price_with_rush.toFixed(2)} ريال (${breakdown.rush_multiplier}x)</span>
                    </div>
                </div>
                <hr class="my-2">
            `;
        }

        if (breakdown.bulk_discount_applied) {
            html += `
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">خصم جماعي:</small><br>
                        <span class="text-success">-${breakdown.bulk_discount_amount.toFixed(2)} ريال</span>
                    </div>
                </div>
                <hr class="my-2">
            `;
        }

        if (breakdown.tax_applied) {
            html += `
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">الضريبة:</small><br>
                        <span>${breakdown.tax_amount.toFixed(2)} ريال</span>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">المجموع النهائي:</small><br>
                        <strong class="text-success">${breakdown.final_price.toFixed(2)} ريال</strong>
                    </div>
                </div>
            `;
        } else {
            html += `
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">المجموع النهائي:</small><br>
                        <strong class="text-success">${breakdown.final_price.toFixed(2)} ريال</strong>
                    </div>
                </div>
            `;
        }

        content.innerHTML = html;
        document.getElementById('price-breakdown').style.display = 'block';
    }

    // Update delivery date based on rush order
    function updateDeliveryDate() {
        if (!deliveryInput.value) {
            const today = new Date();
            const isRush = rushCheckbox.checked;
            const daysToAdd = isRush ? 3 : 7; // Default values, can be fetched from settings

            today.setDate(today.getDate() + daysToAdd);
            deliveryInput.value = today.toISOString().split('T')[0];
        }
    }

    // Event listeners
    fabricSelect.addEventListener('change', calculatePrice);
    piecesInput.addEventListener('input', calculatePrice);
    rushCheckbox.addEventListener('change', function() {
        calculatePrice();
        updateDeliveryDate();
    });

    // Client selection handler
    clientSelect.addEventListener('change', function() {
        if (this.value) {
            document.getElementById('client-info').style.display = 'block';
            document.getElementById('client-details').innerHTML = `
                <small class="text-muted">
                    تم اختيار العميل. يمكنك الآن إكمال تفاصيل الطلب.
                </small>
            `;
        } else {
            document.getElementById('client-info').style.display = 'none';
        }
    });

    // File upload preview
    const fabricPhotoInput = document.getElementById('fabric_photo');
    if (fabricPhotoInput) {
        fabricPhotoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                console.log('Image selected:', file.name);
            }
        });
    }

    // Initial setup
    updateDeliveryDate();
    calculatePrice();

    // Focus on first input
    clientSelect.focus();
});
</script>
{% endblock %}
