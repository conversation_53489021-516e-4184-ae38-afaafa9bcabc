{% extends "base.html" %}

{% block title %}تعديل {{ user.full_name }} - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-edit me-2"></i>تعديل المستخدم: {{ user.full_name }}</h2>
    <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للمستخدمين
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>تعديل بيانات {{ user.username }}</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                            {% if form.full_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.full_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            </div>
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.role.label(class="form-label") }}
                            {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                            {% if form.role.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.role.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if user.id == current_user.id %}
                            <div class="form-text text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                لا يمكنك تغيير دورك الخاص
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.password.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), placeholder="اتركها فارغة للاحتفاظ بكلمة المرور الحالية") }}
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حالة المستخدم</label>
                            <div class="form-check">
                                {{ form.is_active(class="form-check-input", disabled=(user.id == current_user.id)) }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                            {% if user.id == current_user.id %}
                            <div class="form-text text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                لا يمكنك تعطيل حسابك الخاص
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- User Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات المستخدم</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>تاريخ الإنشاء:</strong> {{ user.created_at.strftime('%Y-%m-%d %H:%M') }}<br>
                            <strong>آخر دخول:</strong> 
                            {% if user.last_login %}
                                {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                لم يدخل بعد
                            {% endif %}<br>
                            <strong>الدور الحالي:</strong> 
                            {% if user.role == 'admin' %}
                                <span class="badge bg-danger">مدير</span>
                            {% elif user.role == 'operator' %}
                                <span class="badge bg-warning">مشغل</span>
                            {% else %}
                                <span class="badge bg-secondary">مشاهد</span>
                            {% endif %}
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>الحالة:</strong> 
                            {% if user.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">معطل</span>
                            {% endif %}<br>
                            <strong>رقم المستخدم:</strong> #{{ user.id }}<br>
                            {% if user.id == current_user.id %}
                            <strong class="text-info">
                                <i class="fas fa-user me-1"></i>هذا حسابك الحالي
                            </strong>
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Disable role and active status for current user
{% if user.id == current_user.id %}
document.getElementById('role').disabled = true;
document.getElementById('is_active').disabled = true;
{% endif %}

// Focus on first input
document.getElementById('username').focus();
</script>
{% endblock %}
