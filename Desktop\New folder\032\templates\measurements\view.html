{% extends "base.html" %}

{% block title %}مقاسات {{ measurement.client.full_name }} - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-ruler me-2"></i>مقاسات {{ measurement.client.full_name }}</h2>
    <div>
        <a href="{{ url_for('edit_measurement', id=measurement.id) }}" class="btn btn-warning">
            <i class="fas fa-edit me-2"></i>تعديل المقاسات
        </a>
        <a href="{{ url_for('add_order') }}?client_id={{ measurement.client.id }}" class="btn btn-success">
            <i class="fas fa-plus me-2"></i>إضافة طلب
        </a>
        <a href="{{ url_for('measurements') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للمقاسات
        </a>
    </div>
</div>

<div class="row">
    <!-- Measurement Details -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-ruler-combined me-2"></i>تفاصيل المقاسات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Basic Measurements -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">المقاسات الأساسية</h6>

                        <div class="mb-3">
                            <strong>محيط الرقبة:</strong><br>
                            <span class="text-muted">{{ measurement.neck or 'غير محدد' }} سم</span>
                        </div>

                        <div class="mb-3">
                            <strong>عرض الكتف:</strong><br>
                            <span class="text-muted">{{ measurement.shoulder or 'غير محدد' }} سم</span>
                        </div>

                        <div class="mb-3">
                            <strong>طول الكم:</strong><br>
                            <span class="text-muted">{{ measurement.sleeve or 'غير محدد' }} سم</span>
                        </div>

                        <div class="mb-3">
                            <strong>طول الثوب:</strong><br>
                            <span class="text-muted">{{ measurement.length or 'غير محدد' }} سم</span>
                        </div>
                    </div>

                    <!-- Detailed Measurements -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">المقاسات التفصيلية</h6>

                        <div class="mb-3">
                            <strong>عرض الصدر:</strong><br>
                            <span class="text-muted">{{ measurement.chest_width or 'غير محدد' }} سم</span>
                        </div>

                        <div class="mb-3">
                            <strong>طول السحاب:</strong><br>
                            <span class="text-muted">{{ measurement.zipper_length or 'غير محدد' }} سم</span>
                        </div>

                        <div class="mb-3">
                            <strong>عرض الأسفل:</strong><br>
                            <span class="text-muted">{{ measurement.lower_width or 'غير محدد' }} سم</span>
                        </div>

                        <div class="mb-3">
                            <strong>نوع القماش:</strong><br>
                            <span class="text-muted">{{ measurement.fabric_type or 'غير محدد' }}</span>
                        </div>

                        <div class="mb-3">
                            <strong>زنجفاش:</strong><br>
                            <span class="text-muted">{{ 'نعم' if measurement.has_zhengvash else 'لا' }}</span>
                        </div>
                    </div>
                </div>

                {% if measurement.notes %}
                <hr>
                <div class="mb-3">
                    <strong>ملاحظات:</strong><br>
                    <div class="bg-light p-3 rounded">
                        {{ measurement.notes|replace('\n', '<br>')|safe }}
                    </div>
                </div>
                {% endif %}

                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>تاريخ الإضافة:</strong> {{ measurement.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                    </div>
                    {% if measurement.updated_at and measurement.updated_at != measurement.created_at %}
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>آخر تحديث:</strong> {{ measurement.updated_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Client Info and Actions -->
    <div class="col-md-4 mb-4">
        <!-- Client Information -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل</h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>الاسم:</strong><br>
                    <small>{{ measurement.client.full_name }}</small>
                </div>
                <div class="mb-2">
                    <strong>الجوال:</strong><br>
                    <small>{{ measurement.client.mobile_number }}</small>
                </div>
                {% if measurement.client.address %}
                <div class="mb-2">
                    <strong>العنوان:</strong><br>
                    <small>{{ measurement.client.address }}</small>
                </div>
                {% endif %}

                <div class="d-grid mt-3">
                    <a href="{{ url_for('view_client', id=measurement.client.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-2"></i>عرض العميل
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('add_order') }}?client_id={{ measurement.client.id }}" class="btn btn-success btn-sm">
                        <i class="fas fa-plus me-2"></i>إضافة طلب جديد
                    </a>
                    <a href="{{ url_for('edit_measurement', id=measurement.id) }}" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit me-2"></i>تعديل المقاسات
                    </a>
                    <a href="{{ url_for('measurements') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list me-2"></i>جميع المقاسات
                    </a>
                </div>
            </div>
        </div>

        <!-- Measurement Summary -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>ملخص المقاسات</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-primary mb-0">
                                {% set filled_count = 0 %}
                                {% if measurement.height %}{% set filled_count = filled_count + 1 %}{% endif %}
                                {% if measurement.chest %}{% set filled_count = filled_count + 1 %}{% endif %}
                                {% if measurement.waist %}{% set filled_count = filled_count + 1 %}{% endif %}
                                {% if measurement.hip %}{% set filled_count = filled_count + 1 %}{% endif %}
                                {% if measurement.shoulder %}{% set filled_count = filled_count + 1 %}{% endif %}
                                {% if measurement.sleeve_length %}{% set filled_count = filled_count + 1 %}{% endif %}
                                {% if measurement.thobe_length %}{% set filled_count = filled_count + 1 %}{% endif %}
                                {% if measurement.neck %}{% set filled_count = filled_count + 1 %}{% endif %}
                                {{ filled_count }}
                            </h5>
                            <small class="text-muted">مقاسات مملوءة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-warning mb-0">{{ 8 - filled_count }}</h5>
                        <small class="text-muted">مقاسات فارغة</small>
                    </div>
                </div>

                <div class="progress mt-3" style="height: 8px;">
                    <div class="progress-bar" role="progressbar"
                         style="width: {{ (filled_count / 8 * 100)|round }}%"
                         aria-valuenow="{{ filled_count }}" aria-valuemin="0" aria-valuemax="8">
                    </div>
                </div>
                <small class="text-muted">اكتمال المقاسات: {{ (filled_count / 8 * 100)|round }}%</small>
            </div>
        </div>
    </div>
</div>

<!-- Related Orders -->
{% set client_orders = measurement.client.orders %}
{% if client_orders %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>طلبات العميل ({{ client_orders|length }})</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>نوع القماش</th>
                        <th>عدد القطع</th>
                        <th>السعر</th>
                        <th>الحالة</th>
                        <th>تاريخ الطلب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in client_orders[:5] %}
                    <tr>
                        <td>#{{ order.id }}</td>
                        <td>{{ order.fabric_type }}</td>
                        <td>{{ order.pieces_count }}</td>
                        <td>{{ "%.2f"|format(order.total_price) }} ريال</td>
                        <td>
                            <span class="badge bg-{{ order.get_status_color() }}">
                                {{ order.get_status_display() }}
                            </span>
                        </td>
                        <td>{{ order.order_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <a href="{{ url_for('view_order', id=order.id) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if client_orders|length > 5 %}
        <div class="text-center mt-3">
            <a href="{{ url_for('view_client', id=measurement.client.id) }}" class="btn btn-outline-primary btn-sm">
                عرض جميع الطلبات ({{ client_orders|length }})
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}
{% endblock %}
