{% extends "base.html" %}

{% block title %}إدارة المستخدمين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users-cog me-2"></i>إدارة المستخدمين</h2>
    <a href="{{ url_for('add_user') }}" class="btn btn-primary">
        <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المستخدمين</h5>
    </div>
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم المستخدم</th>
                        <th>الاسم الكامل</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.full_name }}</td>
                        <td>{{ user.role }}</td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">معطل</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('edit_user', id=user.id) }}"
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.id != current_user.id %}
                                <form method="POST" action="{{ url_for('delete_user', id=user.id) }}"
                                      style="display: inline;"
                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% else %}
                                <button class="btn btn-outline-secondary" disabled title="لا يمكن حذف حسابك">
                                    <i class="fas fa-ban"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center text-muted py-5">
            <h5>لا توجد مستخدمين</h5>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
