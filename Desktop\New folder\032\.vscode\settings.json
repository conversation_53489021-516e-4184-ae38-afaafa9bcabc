{"python.defaultInterpreterPath": "python", "python.analysis.extraPaths": ["."], "python.analysis.autoSearchPaths": true, "python.analysis.diagnosticMode": "workspace", "python.analysis.typeCheckingMode": "basic", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "pylance.insidersChannel": "off", "python.analysis.autoImportCompletions": true, "python.analysis.completeFunctionParens": true, "files.associations": {"*.html": "html", "*.css": "css", "*.js": "javascript"}, "emmet.includeLanguages": {"jinja-html": "html"}}