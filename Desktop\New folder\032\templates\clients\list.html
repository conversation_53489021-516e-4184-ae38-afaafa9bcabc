{% extends "base.html" %}

{% block title %}العملاء - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users me-2"></i>إدارة العملاء</h2>
    <a href="{{ url_for('add_client') }}" class="btn btn-primary">
        <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
    </a>
</div>

<!-- Search Bar -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-8">
                <input type="text" class="form-control" name="search"
                       placeholder="البحث بالاسم أو رقم الجوال..."
                       value="{{ search }}">
            </div>
            <div class="col-md-4">
                <div class="d-grid gap-2 d-md-flex">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                    <a href="{{ url_for('clients') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Clients Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة العملاء ({{ clients.total }} عميل)</h5>
    </div>
    <div class="card-body">
        {% if clients.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>الاسم الكامل</th>
                        <th>رقم الجوال</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client in clients.items %}
                    <tr>
                        <td>{{ client.id }}</td>
                        <td>
                            <a href="{{ url_for('view_client', id=client.id) }}"
                               class="text-decoration-none fw-bold">
                                {{ client.full_name }}
                            </a>
                        </td>
                        <td>
                            <i class="fas fa-phone me-1 text-muted"></i>
                            {{ client.mobile_number }}
                        </td>
                        <td>{{ client.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('view_client', id=client.id) }}"
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_client', id=client.id) }}"
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('add_measurement', client_id=client.id) }}"
                                   class="btn btn-outline-success" title="إضافة مقاسات">
                                    <i class="fas fa-ruler"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if clients.pages > 1 %}
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if clients.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('clients', page=clients.prev_num, search=search) }}">
                        السابق
                    </a>
                </li>
                {% endif %}

                {% for page_num in clients.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != clients.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('clients', page=page_num, search=search) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if clients.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('clients', page=clients.next_num, search=search) }}">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center text-muted py-5">
            <i class="fas fa-users fa-3x mb-3"></i>
            <h5>لا توجد عملاء</h5>
            <p>لم يتم العثور على أي عملاء{% if search %} تطابق البحث "{{ search }}"{% endif %}</p>
            <a href="{{ url_for('add_client') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>إضافة أول عميل
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
