{% extends "base.html" %}

{% block title %}إضافة مستخدم جديد - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد</h2>
    <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للمستخدمين
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>بيانات المستخدم الجديد</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">اسم المستخدم يجب أن يكون فريداً</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                            {% if form.full_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.full_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            </div>
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">البريد الإلكتروني اختياري</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.role.label(class="form-label") }}
                            {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                            {% if form.role.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.role.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.password.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">إذا تُركت فارغة، ستكون كلمة المرور: 123456</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حالة المستخدم</label>
                            <div class="form-check">
                                {{ form.is_active(class="form-check-input") }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ المستخدم
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الأدوار</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-user-shield fa-2x text-danger mb-2"></i>
                            <h6 class="text-danger">مدير</h6>
                            <small class="text-muted">
                                صلاحيات كاملة للنظام<br>
                                إدارة المستخدمين والإعدادات
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-user-cog fa-2x text-warning mb-2"></i>
                            <h6 class="text-warning">مشغل</h6>
                            <small class="text-muted">
                                إدارة العملاء والطلبات<br>
                                إنشاء التقارير
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-user fa-2x text-secondary mb-2"></i>
                            <h6 class="text-secondary">مشاهد</h6>
                            <small class="text-muted">
                                عرض البيانات فقط<br>
                                بدون صلاحيات تعديل
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Generate username from full name
document.getElementById('full_name').addEventListener('input', function() {
    const fullName = this.value;
    const usernameField = document.getElementById('username');
    
    if (fullName && !usernameField.value) {
        // Simple username generation (you can make this more sophisticated)
        let username = fullName.toLowerCase()
            .replace(/\s+/g, '')
            .replace(/[أإآ]/g, 'a')
            .replace(/[ة]/g, 'h')
            .replace(/[ي]/g, 'y')
            .substring(0, 15);
        
        usernameField.value = username;
    }
});

// Password strength indicator
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = document.getElementById('password-strength');
    
    if (password.length === 0) {
        return;
    }
    
    let score = 0;
    if (password.length >= 6) score++;
    if (password.match(/[a-z]/)) score++;
    if (password.match(/[A-Z]/)) score++;
    if (password.match(/[0-9]/)) score++;
    if (password.match(/[^a-zA-Z0-9]/)) score++;
    
    const levels = ['ضعيف جداً', 'ضعيف', 'متوسط', 'قوي', 'قوي جداً'];
    const colors = ['danger', 'warning', 'info', 'success', 'success'];
    
    if (!strength) {
        const strengthDiv = document.createElement('div');
        strengthDiv.id = 'password-strength';
        strengthDiv.className = 'form-text';
        this.parentNode.parentNode.appendChild(strengthDiv);
    }
    
    document.getElementById('password-strength').innerHTML = 
        `<span class="text-${colors[score-1]}">قوة كلمة المرور: ${levels[score-1]}</span>`;
});

// Focus on first input
document.getElementById('username').focus();
</script>
{% endblock %}
