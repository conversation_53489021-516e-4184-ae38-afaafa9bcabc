from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed
from wtforms import <PERSON>Field, TextAreaField, FloatField, IntegerField, SelectField, DateField, BooleanField, PasswordField, DecimalField
from wtforms.validators import DataRequired, Length, Email, Optional, NumberRange, ValidationError
from wtforms.widgets import TextArea

class LoginForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])

class ClientForm(FlaskForm):
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
    mobile_number = StringField('رقم الجوال', validators=[DataRequired(), Length(min=8, max=20)])
    address = Text<PERSON>reaField('العنوان', validators=[Optional()], widget=TextArea())

class MeasurementForm(FlaskForm):
    neck = FloatField('محيط الرقبة (سم)', validators=[Optional(), NumberRange(min=0, max=100)])
    shoulder = FloatField('عرض الكتف (سم)', validators=[Optional(), NumberRange(min=0, max=100)])
    sleeve = FloatField('طول الكم (سم)', validators=[Optional(), NumberRange(min=0, max=150)])
    length = FloatField('طول الثوب (سم)', validators=[Optional(), NumberRange(min=0, max=200)])
    chest_width = FloatField('عرض الصدر (سم)', validators=[Optional(), NumberRange(min=0, max=150)])
    zipper_length = FloatField('طول السحاب (سم)', validators=[Optional(), NumberRange(min=0, max=50)])
    has_zhengvash = BooleanField('يحتوي على زنجفاش')
    lower_width = FloatField('عرض الأسفل (سم)', validators=[Optional(), NumberRange(min=0, max=200)])
    fabric_type = SelectField('نوع القماش', choices=[
        ('', 'اختر نوع القماش'),
        ('cotton', 'قطن'),
        ('wool', 'صوف'),
        ('silk', 'حرير'),
        ('linen', 'كتان'),
        ('polyester', 'بوليستر'),
        ('mixed', 'مخلوط')
    ], validators=[Optional()])
    notes = TextAreaField('ملاحظات', validators=[Optional()], widget=TextArea())

class OrderForm(FlaskForm):
    client_id = SelectField('العميل', coerce=int, validators=[DataRequired()])
    fabric_type = SelectField('نوع القماش', validators=[DataRequired()])
    pieces_count = IntegerField('عدد القطع', validators=[DataRequired(), NumberRange(min=1, max=50)], default=1)
    is_rush_order = BooleanField('طلب عاجل', default=False)
    total_price = FloatField('السعر الإجمالي (ريال)', validators=[DataRequired(), NumberRange(min=0)])
    expected_delivery = DateField('تاريخ التسليم المتوقع', validators=[Optional()])
    fabric_photo = FileField('صورة القماش', validators=[
        Optional(),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'الصور فقط!')
    ])
    special_requests = TextAreaField('طلبات خاصة', validators=[Optional()], widget=TextArea())
    notes = TextAreaField('ملاحظات', validators=[Optional()], widget=TextArea())

    def __init__(self, *args, **kwargs):
        super(OrderForm, self).__init__(*args, **kwargs)
        # Load fabric types from pricing rules
        from models import PricingRule
        pricing_rules = PricingRule.query.filter_by(is_active=True).all()
        self.fabric_type.choices = [(rule.fabric_type, rule.fabric_type) for rule in pricing_rules]

        # Add fallback options if no pricing rules exist
        if not self.fabric_type.choices:
            self.fabric_type.choices = [
                ('ثوب صيفي قطني', 'ثوب صيفي قطني'),
                ('ثوب شتوي صوفي', 'ثوب شتوي صوفي'),
                ('بشت عادي', 'بشت عادي'),
                ('دشداشة كويتية', 'دشداشة كويتية')
            ]

class OrderStatusForm(FlaskForm):
    status = SelectField('حالة الطلب', choices=[
        ('new', 'جديد'),
        ('ongoing', 'قيد التنفيذ'),
        ('ready', 'جاهز'),
        ('delivered', 'تم التسليم')
    ], validators=[DataRequired()])
    actual_delivery = DateField('تاريخ التسليم الفعلي', validators=[Optional()])
    notes = TextAreaField('ملاحظات التحديث', validators=[Optional()], widget=TextArea())
    notify_client = BooleanField('إشعار العميل بالتحديث', default=True)

class UserForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    email = StringField('البريد الإلكتروني', validators=[Optional(), Email()])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
    password = PasswordField('كلمة المرور', validators=[Optional(), Length(min=6)])
    role = SelectField('الدور', choices=[
        ('admin', 'مدير'),
        ('operator', 'مشغل'),
        ('viewer', 'مشاهد')
    ], validators=[DataRequired()])
    is_active = BooleanField('نشط', default=True)

class ProfileForm(FlaskForm):
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
    email = StringField('البريد الإلكتروني', validators=[Optional(), Email()])
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)])
    current_password = PasswordField('كلمة المرور الحالية', validators=[Optional()])
    new_password = PasswordField('كلمة المرور الجديدة', validators=[Optional(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[Optional()])

    # Notification preferences
    email_notifications = BooleanField('إشعارات البريد الإلكتروني', default=True)
    sms_notifications = BooleanField('إشعارات الرسائل النصية', default=True)
    desktop_notifications = BooleanField('إشعارات سطح المكتب', default=True)

    def validate_confirm_password(self, field):
        if self.new_password.data and field.data != self.new_password.data:
            raise ValidationError('كلمات المرور غير متطابقة')

class SearchForm(FlaskForm):
    query = StringField('البحث', validators=[Optional()])

class PricingRuleForm(FlaskForm):
    fabric_type = StringField('نوع القماش', validators=[DataRequired(), Length(min=2, max=50)])
    base_price = DecimalField('السعر الأساسي (ريال)', validators=[DataRequired(), NumberRange(min=0)])
    size_multiplier = DecimalField('مضاعف الحجم', validators=[DataRequired(), NumberRange(min=0.1, max=5.0)], default=1.0)
    rush_multiplier = DecimalField('مضاعف الطلب العاجل', validators=[DataRequired(), NumberRange(min=1.0, max=10.0)], default=1.5)
    is_active = BooleanField('نشط', default=True)

class SystemSettingsForm(FlaskForm):
    # Company Settings
    company_name = StringField('اسم الشركة', validators=[DataRequired()])
    company_address = TextAreaField('عنوان الشركة', validators=[Optional()])
    company_phone = StringField('هاتف الشركة', validators=[Optional()])
    company_email = StringField('بريد الشركة', validators=[Optional(), Email()])

    # Pricing Settings
    default_delivery_days = IntegerField('أيام التسليم الافتراضية', validators=[DataRequired(), NumberRange(min=1, max=365)], default=7)
    rush_order_days = IntegerField('أيام الطلب العاجل', validators=[DataRequired(), NumberRange(min=1, max=30)], default=3)
    minimum_order_amount = DecimalField('الحد الأدنى للطلب (ريال)', validators=[DataRequired(), NumberRange(min=0)], default=50.0)

    # Discount Settings
    bulk_discount_threshold = IntegerField('عدد القطع للخصم الجماعي', validators=[DataRequired(), NumberRange(min=2, max=100)], default=5)
    bulk_discount_percentage = DecimalField('نسبة الخصم الجماعي (%)', validators=[DataRequired(), NumberRange(min=0, max=50)], default=10.0)
    vip_discount_percentage = DecimalField('خصم العملاء المميزين (%)', validators=[DataRequired(), NumberRange(min=0, max=50)], default=15.0)

    # Notification Settings
    sms_notifications = BooleanField('إشعارات SMS', default=True)
    email_notifications = BooleanField('إشعارات البريد الإلكتروني', default=False)
    whatsapp_notifications = BooleanField('إشعارات واتساب', default=False)

    # System Settings
    auto_backup = BooleanField('النسخ الاحتياطي التلقائي', default=True)
    backup_frequency = SelectField('تكرار النسخ الاحتياطي', choices=[
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري')
    ], default='weekly')

    # Tax Settings
    tax_enabled = BooleanField('تفعيل الضريبة', default=True)
    tax_percentage = DecimalField('نسبة الضريبة (%)', validators=[NumberRange(min=0, max=100)], default=15.0)
    tax_number = StringField('الرقم الضريبي', validators=[Optional()])
