{% extends "base.html" %}

{% block title %}تعديل مقاسات {{ measurement.client.full_name }} - نظام إدارة خياطة الرجال القطري{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-edit me-2"></i>تعديل مقاسات {{ measurement.client.full_name }}</h2>
    <div>
        <a href="{{ url_for('view_measurement', id=measurement.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للمقاسات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-ruler me-2"></i>تعديل المقاسات</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}

                    <!-- Basic Measurements -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted mb-3 border-bottom pb-2">المقاسات الأساسية</h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.neck.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.neck(class="form-control" + (" is-invalid" if form.neck.errors else "")) }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.neck.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.neck.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.shoulder.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.shoulder(class="form-control" + (" is-invalid" if form.shoulder.errors else "")) }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.shoulder.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.shoulder.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.sleeve.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.sleeve(class="form-control" + (" is-invalid" if form.sleeve.errors else "")) }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.sleeve.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.sleeve.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.length.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.length(class="form-control" + (" is-invalid" if form.length.errors else "")) }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.length.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.length.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Detailed Measurements -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted mb-3 border-bottom pb-2">المقاسات التفصيلية</h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.chest_width.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.chest_width(class="form-control" + (" is-invalid" if form.chest_width.errors else "")) }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.chest_width.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.chest_width.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.zipper_length.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.zipper_length(class="form-control" + (" is-invalid" if form.zipper_length.errors else "")) }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.zipper_length.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.zipper_length.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.lower_width.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.lower_width(class="form-control" + (" is-invalid" if form.lower_width.errors else "")) }}
                                <span class="input-group-text">سم</span>
                            </div>
                            {% if form.lower_width.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.lower_width.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.fabric_type.label(class="form-label") }}
                            {{ form.fabric_type(class="form-select" + (" is-invalid" if form.fabric_type.errors else "")) }}
                            {% if form.fabric_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.fabric_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.has_zhengvash(class="form-check-input") }}
                                {{ form.has_zhengvash.label(class="form-check-label") }}
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted mb-3 border-bottom pb-2">ملاحظات إضافية</h6>
                        </div>

                        <div class="col-12 mb-3">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="4") }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.notes.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">أي ملاحظات خاصة بالمقاسات أو تفضيلات العميل</div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                        <a href="{{ url_for('view_measurement', id=measurement.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Client Info -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل</h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>الاسم:</strong><br>
                    <small>{{ measurement.client.full_name }}</small>
                </div>
                <div class="mb-2">
                    <strong>الجوال:</strong><br>
                    <small>{{ measurement.client.mobile_number }}</small>
                </div>
                {% if measurement.client.address %}
                <div class="mb-2">
                    <strong>العنوان:</strong><br>
                    <small>{{ measurement.client.address }}</small>
                </div>
                {% endif %}

                <div class="d-grid mt-3">
                    <a href="{{ url_for('view_client', id=measurement.client.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-2"></i>عرض العميل
                    </a>
                </div>
            </div>
        </div>

        <!-- Measurement Tips -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>نصائح القياس</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <strong>الطول:</strong> من أعلى الرأس إلى الأرض
                    </div>
                    <div class="mb-2">
                        <strong>الصدر:</strong> حول أوسع جزء من الصدر
                    </div>
                    <div class="mb-2">
                        <strong>الخصر:</strong> حول أضيق جزء من الخصر
                    </div>
                    <div class="mb-2">
                        <strong>الكتف:</strong> من كتف إلى آخر
                    </div>
                    <div class="mb-2">
                        <strong>طول الكم:</strong> من الكتف إلى المعصم
                    </div>
                    <div class="mb-2">
                        <strong>طول الثوب:</strong> من الكتف إلى الطول المطلوب
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
