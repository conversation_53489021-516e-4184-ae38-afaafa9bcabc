from models import db, Order, Client
from datetime import datetime, timedelta
from sqlalchemy import func, extract

def generate_daily_report(target_date=None):
    """Generate daily sales and orders report"""
    if not target_date:
        target_date = datetime.now().date()
    
    # Orders for the day
    daily_orders = Order.query.filter(
        func.date(Order.order_date) == target_date
    ).all()
    
    # Statistics
    total_orders = len(daily_orders)
    total_revenue = sum(order.total_price for order in daily_orders)
    
    # Orders by status
    status_counts = {}
    for order in daily_orders:
        status_counts[order.status] = status_counts.get(order.status, 0) + 1
    
    # Orders by fabric type
    fabric_counts = {}
    for order in daily_orders:
        fabric_counts[order.fabric_type] = fabric_counts.get(order.fabric_type, 0) + 1
    
    return {
        'date': target_date,
        'total_orders': total_orders,
        'total_revenue': total_revenue,
        'orders': daily_orders,
        'status_counts': status_counts,
        'fabric_counts': fabric_counts
    }

def generate_monthly_report(year=None, month=None):
    """Generate monthly sales and orders report"""
    if not year:
        year = datetime.now().year
    if not month:
        month = datetime.now().month
    
    # Orders for the month
    monthly_orders = Order.query.filter(
        extract('year', Order.order_date) == year,
        extract('month', Order.order_date) == month
    ).all()
    
    # Statistics
    total_orders = len(monthly_orders)
    total_revenue = sum(order.total_price for order in monthly_orders)
    
    # Daily breakdown
    daily_stats = {}
    for order in monthly_orders:
        day = order.order_date.day
        if day not in daily_stats:
            daily_stats[day] = {'orders': 0, 'revenue': 0}
        daily_stats[day]['orders'] += 1
        daily_stats[day]['revenue'] += order.total_price
    
    # Orders by status
    status_counts = {}
    for order in monthly_orders:
        status_counts[order.status] = status_counts.get(order.status, 0) + 1
    
    # Orders by fabric type
    fabric_counts = {}
    for order in monthly_orders:
        fabric_counts[order.fabric_type] = fabric_counts.get(order.fabric_type, 0) + 1
    
    # Top clients
    client_stats = {}
    for order in monthly_orders:
        client_id = order.client_id
        if client_id not in client_stats:
            client_stats[client_id] = {
                'client': order.client,
                'orders': 0,
                'revenue': 0
            }
        client_stats[client_id]['orders'] += 1
        client_stats[client_id]['revenue'] += order.total_price
    
    # Sort clients by revenue
    top_clients = sorted(client_stats.values(), 
                        key=lambda x: x['revenue'], reverse=True)[:10]
    
    return {
        'year': year,
        'month': month,
        'total_orders': total_orders,
        'total_revenue': total_revenue,
        'daily_stats': daily_stats,
        'status_counts': status_counts,
        'fabric_counts': fabric_counts,
        'top_clients': top_clients,
        'orders': monthly_orders
    }

def get_dashboard_stats():
    """Get statistics for dashboard"""
    now = datetime.now()
    
    # Total counts
    total_clients = Client.query.count()
    total_orders = Order.query.count()
    
    # Orders by status
    pending_orders = Order.query.filter(Order.status.in_(['new', 'ongoing'])).count()
    ready_orders = Order.query.filter_by(status='ready').count()
    delivered_orders = Order.query.filter_by(status='delivered').count()
    
    # This month's revenue
    monthly_revenue = db.session.query(func.sum(Order.total_price)).filter(
        extract('month', Order.order_date) == now.month,
        extract('year', Order.order_date) == now.year
    ).scalar() or 0
    
    # This week's orders
    week_start = now - timedelta(days=now.weekday())
    weekly_orders = Order.query.filter(Order.order_date >= week_start).count()
    
    # Recent orders
    recent_orders = Order.query.order_by(Order.created_at.desc()).limit(5).all()
    
    return {
        'total_clients': total_clients,
        'total_orders': total_orders,
        'pending_orders': pending_orders,
        'ready_orders': ready_orders,
        'delivered_orders': delivered_orders,
        'monthly_revenue': monthly_revenue,
        'weekly_orders': weekly_orders,
        'recent_orders': recent_orders
    }
