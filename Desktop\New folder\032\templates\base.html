<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة خياطة الرجال القطري{% endblock %}</title>

    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.2rem;
        }

        .sidebar {
            min-height: calc(100vh - 56px);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(-5px);
        }

        .sidebar .nav-link i {
            margin-left: 0.5rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            padding: 2rem;
        }

        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.75rem 0.75rem 0 0 !important;
            font-weight: 500;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(102, 126, 234, 0.4);
        }

        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 500;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
        }

        .alert {
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
        }

        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .measurement-diagram {
            max-width: 400px;
            margin: 0 auto;
        }

        .status-new { background-color: #17a2b8; }
        .status-ongoing { background-color: #ffc107; color: #000; }
        .status-ready { background-color: #28a745; }
        .status-delivered { background-color: #6c757d; }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-cut me-2"></i>
                نظام إدارة خياطة الرجال القطري
            </a>

            {% if current_user.is_authenticated %}
            <div class="navbar-nav ms-auto">
                <!-- Notifications -->
                <div class="nav-item dropdown me-2">
                    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown"
                       role="button" data-bs-toggle="dropdown" onclick="loadRecentNotifications(); playClickSound();">
                        <i class="fas fa-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                              id="notification-badge" style="display: none;">
                            0
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end notification-dropdown" style="width: 350px;">
                        <li class="dropdown-header d-flex justify-content-between align-items-center">
                            <span>الإشعارات</span>
                            <a href="{{ url_for('notifications') }}" class="btn btn-sm btn-outline-primary">
                                عرض الكل
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <div id="recent-notifications">
                            <li class="dropdown-item text-center text-muted">
                                <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                            </li>
                        </div>
                    </ul>
                </div>

                <!-- User Menu -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        {{ current_user.full_name }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('notifications') }}">
                            <i class="fas fa-bell me-2"></i>الإشعارات
                        </a></li>
                        {% if current_user.is_admin() %}
                        <li><a class="dropdown-item" href="{{ url_for('system_settings') }}">
                            <i class="fas fa-cog me-2"></i>إعدادات النظام
                        </a></li>
                        {% endif %}
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
            {% endif %}
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <nav class="nav flex-column p-3">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                        <a class="nav-link" href="{{ url_for('clients') }}">
                            <i class="fas fa-users"></i>
                            العملاء
                        </a>
                        <a class="nav-link" href="{{ url_for('orders') }}">
                            <i class="fas fa-shopping-cart"></i>
                            الطلبات
                        </a>
                        <a class="nav-link" href="{{ url_for('measurements') }}">
                            <i class="fas fa-ruler"></i>
                            المقاسات
                        </a>
                        <a class="nav-link" href="{{ url_for('reports') }}">
                            <i class="fas fa-chart-bar"></i>
                            التقارير
                        </a>
                        <a class="nav-link" href="{{ url_for('pricing_settings') }}">
                            <i class="fas fa-tags"></i>
                            إعدادات التسعير
                        </a>
                        <a class="nav-link" href="{{ url_for('notifications') }}">
                            <i class="fas fa-bell"></i>
                            الإشعارات
                            <span class="badge bg-danger ms-2" id="sidebar-notification-badge" style="display: none;">0</span>
                        </a>
                        {% if current_user.is_admin() %}
                        <a class="nav-link" href="{{ url_for('users') }}">
                            <i class="fas fa-user-cog"></i>
                            إدارة المستخدمين
                        </a>
                        <a class="nav-link" href="{{ url_for('notifications_admin_panel') }}">
                            <i class="fas fa-bell-slash"></i>
                            لوحة تحكم الإشعارات
                        </a>
                        <a class="nav-link" href="{{ url_for('system_settings') }}">
                            <i class="fas fa-cog"></i>
                            إعدادات النظام
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content">
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </div>
            {% else %}
            <!-- Full width for login page -->
            <div class="col-12">
                {% block login_content %}{% endblock %}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js for reports -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Notifications JavaScript -->
    {% if current_user.is_authenticated %}
    <script>
        // Load unread notifications count on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateNotificationBadge();
            requestNotificationPermission();
            // Update every 5 seconds for better responsiveness
            setInterval(updateNotificationBadge, 5000);
            // Check for new notifications every 5 seconds
            setInterval(checkForNewNotifications, 5000);
        });

        let lastNotificationCount = 0;

        function requestNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission();
            }
        }

        function showDesktopNotification(title, message, icon = '/static/favicon.ico') {
            if ('Notification' in window && Notification.permission === 'granted') {
                const notification = new Notification(title, {
                    body: message,
                    icon: icon,
                    badge: icon,
                    tag: 'sewing-system',
                    requireInteraction: false,
                    silent: false
                });

                // Auto close after 5 seconds
                setTimeout(() => {
                    notification.close();
                }, 5000);

                // Click to focus window
                notification.onclick = function() {
                    window.focus();
                    notification.close();
                };
            }
        }

        function checkForNewNotifications() {
            fetch('/api/notifications/unread-count')
            .then(response => response.json())
            .then(data => {
                if (data.count > lastNotificationCount && lastNotificationCount > 0) {
                    // New notification received
                    showDesktopNotification(
                        'إشعار جديد - نظام إدارة الخياطة',
                        'لديك إشعارات جديدة في النظام'
                    );

                    // Play notification sound
                    playNotificationSound();

                    // Add visual effects
                    const bellIcon = document.querySelector('#notificationsDropdown i');
                    if (bellIcon) {
                        bellIcon.classList.add('bell-shake');
                        setTimeout(() => {
                            bellIcon.classList.remove('bell-shake');
                        }, 500);
                    }
                }
                lastNotificationCount = data.count;
            })
            .catch(error => {
                console.error('Error checking notifications:', error);
            });
        }

        function playNotificationSound() {
            // Create audio element for notification sound
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.5;
            audio.play().catch(e => console.log('Could not play notification sound'));
        }

        // Add click sound for notification dropdown
        function playClickSound() {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.2;
            audio.play().catch(e => console.log('Could not play click sound'));
        }

        function updateNotificationBadge() {
            fetch('/api/notifications/unread-count')
            .then(response => response.json())
            .then(data => {
                const badge = document.getElementById('notification-badge');
                const sidebarBadge = document.getElementById('sidebar-notification-badge');

                if (data.count > 0) {
                    badge.textContent = data.count;
                    badge.style.display = 'block';
                    badge.classList.add('notification-pulse');
                    sidebarBadge.textContent = data.count;
                    sidebarBadge.style.display = 'inline';
                    sidebarBadge.classList.add('notification-pulse');
                } else {
                    badge.style.display = 'none';
                    badge.classList.remove('notification-pulse');
                    sidebarBadge.style.display = 'none';
                    sidebarBadge.classList.remove('notification-pulse');
                }
            })
            .catch(error => {
                console.error('Error updating notification badge:', error);
            });
        }

        function loadRecentNotifications() {
            const container = document.getElementById('recent-notifications');
            container.innerHTML = '<li class="dropdown-item text-center text-muted"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</li>';

            fetch('/api/notifications/recent')
            .then(response => response.json())
            .then(data => {
                if (data.notifications.length === 0) {
                    container.innerHTML = '<li class="dropdown-item text-center text-muted">لا توجد إشعارات</li>';
                    return;
                }

                let html = '';
                data.notifications.forEach(notification => {
                    const isUnread = !notification.is_read ? 'fw-bold' : '';
                    const badge = !notification.is_read ? '<span class="badge bg-primary">جديد</span>' : '';

                    html += `
                        <li class="dropdown-item notification-item ${isUnread}" style="white-space: normal; max-width: 330px;">
                            <div class="d-flex align-items-start">
                                <div class="me-2">
                                    <i class="${notification.icon} text-${notification.color}"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="notification-title">${notification.title} ${badge}</div>
                                    <div class="notification-message text-muted small">${notification.message}</div>
                                    <div class="notification-time text-muted small">
                                        <i class="fas fa-clock"></i> ${notification.created_at}
                                    </div>
                                </div>
                            </div>
                        </li>
                    `;
                });

                container.innerHTML = html;
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
                container.innerHTML = '<li class="dropdown-item text-center text-danger">خطأ في تحميل الإشعارات</li>';
            });
        }
    </script>
    {% endif %}

    <style>
        .notification-dropdown {
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #eee;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item:hover {
            background-color: #f8f9fa;
            cursor: pointer;
        }

        .notification-item.fw-bold {
            background-color: #e3f2fd;
        }

        .notification-item.fw-bold:hover {
            background-color: #bbdefb;
        }

        .notification-title {
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .notification-message {
            font-size: 0.8rem;
            line-height: 1.3;
            margin-bottom: 0.25rem;
        }

        .notification-time {
            font-size: 0.75rem;
        }

        /* Pulse animation for notification badge */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .notification-pulse {
            animation: pulse 2s infinite;
        }

        /* Bell shake animation */
        @keyframes shake {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-10deg); }
            75% { transform: rotate(10deg); }
        }

        .bell-shake {
            animation: shake 0.5s ease-in-out;
        }
    </style>

    {% block extra_js %}{% endblock %}
</body>
</html>
